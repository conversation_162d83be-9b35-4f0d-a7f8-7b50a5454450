<?php
return [
    'frontend_base_url' => 'https://comave.com',
    'cache' => [
        'frontend' => [
            'default' => [
                'backend' => '\\Magento\\Framework\\Cache\\Backend\\RemoteSynchronizedCache',
                'backend_options' => [
                    'remote_backend' => '\\Magento\\Framework\\Cache\\Backend\\Redis',
                    'remote_backend_options' => [
                        'server' => '{{REDIS_HOST}}',
                        'port' => '{{REDIS_PORT}}',
                        'database' => 1,
                        'persistent' => 0,
                        'password' => '',
                        'compress_data' => '1',
                        'load_from_slave' => [
                            'server' => '{{REDIS_HOST}}',
                            'port' => '{{REDIS_PORT}}'
                        ],
                        'read_timeout' => 1,
                        'retry_reads_on_master' => 1
                    ],
                    'local_backend' => 'Cm_Cache_Backend_File',
                    'local_backend_options' => [
                        'server' => '{{REDIS_HOST}}',
                        'port' => '{{REDIS_PORT}}',
                        'persistent' => '',
                        'database' => '15',
                        'force_standalone' => '0',
                        'connect_retries' => '1',
                        'read_timeout' => '10',
                        'automatic_cleaning_factor' => '0',
                        'compress_data' => '1',
                        'compress_tags' => '1',
                        'compress_threshold' => '20480',
                        'compression_lib' => 'gzip'
                ],
                    '_useLua' => false,
                    'use_lua' => false
                ],
                'frontend_options' => [
                    'write_control' => false
                ]
            ]
        ],
        'type' => [
            'default' => [
                'frontend' => 'default'
            ]
        ],
        'graphql' => [
            'id_salt' => '{{GRAPHQL_SALT}}'
        ]
    ],
    'MAGE_MODE' => 'production',
    'cache_types' => [
        'compiled_config' => 1,
        'config' => 1,
        'layout' => 1,
        'block_html' => 1,
        'collections' => 1,
        'reflection' => 1,
        'db_ddl' => 1,
        'eav' => 1,
        'customer_notification' => 1,
        'config_integration' => 1,
        'config_integration_api' => 1,
        'full_page' => 1,
        'target_rule' => 1,
        'config_webservice' => 1,
        'translate' => 1,
        'webhooks_response' => 1,
        'graphql_query_resolver_result' => 1,
        'admin_ui_sdk' => 1
    ],
    'cron' => [],
    'backend' => [
        'frontName' => '{{FE_NAME}}'
    ],
    'remote_storage' => [
        'driver' => 'file'
    ],
    'checkout' => [
        'async' => 0,
        'deferred_total_calculating' => 0
    ],
    'queue' => [
        'amqp' => [
            'host' => '{{AMQP_HOST}}',
            'port' => '{{AMQP_PORT}}',
            'user' => '{{AMQP_USER}}',
            'password' => '{{AMQP_PASSWORD}}',
            'virtualhost' => '{{AMQP_VHOST}}',
            'ssl' => 'true'
        ],
        'consumers_wait_for_messages' => 0
    ],
    'db' => [
        'table_prefix' => '',
        'connection' => [
            'default' => [
                'host' => '{{DB_HOST}}',
                'dbname' => '{{DB_NAME}}',
                'username' => '{{DB_USERNAME}}',
                'password' => '{{DB_PASSWORD}}',
                'model' => 'mysql4',
                'engine' => 'innodb',
                'initStatements' => 'SET NAMES utf8;',
                'active' => '1',
                'persistent' => true,
            ]
        ]
    ],
    'crypt' => [
        'key' => '{{CRYPT_KEY}}'
    ],
    'resource' => [
        'default_setup' => [
            'connection' => 'default'
        ]
    ],
    'x-frame-options' => 'SAMEORIGIN',
    'session' => [
        'save' => 'redis',
        'redis' => [
            'host' => '{{REDIS_SESSION_HOST}}',
            'port' => '{{REDIS_SESSION_PORT}}',
            'database' => {{REDIS_SESSION_DB}},
            'disable_locking' => 1
        ]
    ],
    'lock' => [
        'provider' => 'db',
        'config' => [
            'prefix' => null
        ]
    ],
    'directories' => [
        'document_root_is_pub' => true
    ],
    'downloadable_domains' => [
        '{{DOWNLOADABLE_DOMAIN}}'
    ],
    'install' => [
        'date' => 'Mon, 13 Jun 2022 19:33:34 +0000'
    ],
    'static_content_on_demand_in_production' => 0,
    'force_html_minification' => 1,
    'cron_consumers_runner' => [
        'cron_run' => true,
        'max_messages' => 10000,
        'consumers' => [],
        'multiple_processes' => [
            'sellerProductSync' => 5,
            'shopifyCategoryMatcher' => 2,
            'sellerProductSyncMedia' => 5
        ]
    ],
    'system' => [
        'default' => [
            'web' => [
                'graphql' => [
                    'cors_allowed_origins' => 'https://comave.com',
                    'cors_allowed_methods' => 'GET, POST, PATCH, DELETE, OPTIONS',
                    'cors_allow_credentials' => 1
                ],
                'api_rest' => [
                    'cors_allowed_origins' => 'https://comave.com',
                    'cors_allowed_methods' => 'GET, POST, PATCH, DELETE, OPTIONS',
                    'cors_allow_credentials' => 1,
                ]
            ],
            'catalog' => [
                'search' => [
                    'engine' => 'opensearch',
                    'opensearch_server_hostname' => '{{OPENSEARCH_HOSTNAME}}',
                    'opensearch_server_port' => '{{OPENSEARCH_PORT}}',
                    'opensearch_index_prefix' => '{{OPENSEARCH_INDEX_PREFIX}}'
                ]
            ],
            'cataloginventory' => [
                'options' => [
                    'show_out_of_stock' => 0,
                ],
                'product_options' => [
                    'allow_backorders' => 0
                ]
            ],
            'dev' => [
                'swagger' => [
                    'active' => '0'
                ],
                'static' => [
                    'sign' => '0'
                ]
            ],
            'payment' => [
                'stripe_payments' => [
                    'active' => 1,
                    'payment_flow' => 0,
                    'title' => 'Stripe Credit/Debit Card Payment',
                    'payment_action' => 'authorize_capture',
                    'test_mode' => 1,
                    'risk_level' => 0,
                ],
                'stripe_payments_subscriptions' => [
                    'active' => 0
                ]
            ]
        ]
    ]
];
