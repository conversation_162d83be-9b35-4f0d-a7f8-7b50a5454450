name: Continuous Integration Assessment

on:
  pull_request:
    types: [ opened, synchronize, reopened, ready_for_review ]

jobs:
  continuous-integration:
    if: ${{ success() }}
    runs-on: ubuntu-latest
    steps:
      - name: Check-out Repository under $GITHUB_WORKSPACE
        uses: actions/checkout@v4
      - name: Setup PHP 8.3 environment
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.3'
          tools: composer:v2, cs2pr
          coverage: none
      - name: Cache Composer packages
        id: composer-cache
        uses: actions/cache@v4
        with:
          path: vendor
          key: ${{ runner.os }}-php-${{ hashFiles('**/composer.lock') }}
          restore-keys: |
            ${{ runner.os }}-php-
      - name: Install Dependencies
        run: |
          rm -rf vendor/*
          composer install --prefer-dist --no-interaction --no-progress --ansi
      - name: Get Changed Files
        id: changed-files
        uses: tj-actions/changed-files@v45
      - name: Run PHP Static Analysis Tool
        continue-on-error: true
        env:
          ALL_CHANGED_FILES: ${{ steps.changed-files.outputs.all_changed_files }}
        run: |
          for affected_file in ${ALL_CHANGED_FILES}; do
            if [ -e "${affected_file}" ] && [[ "${affected_file}" == *.php ]]; then
              echo "Running static analysis tool for the [ ${affected_file} ] file."
              ./vendor/bin/phpstan analyse --level=0 --no-progress --no-interaction --ansi "${affected_file}"
            fi
          done
      - name: Check Coding Standards
        continue-on-error: true
        env:
          ALL_CHANGED_FILES: ${{ steps.changed-files.outputs.all_changed_files }}
        run: |
          for affected_file in ${ALL_CHANGED_FILES}; do
            if [ -e "${affected_file}" ]; then
              echo "Checking coding standards for the [ ${affected_file} ] file."
              ./vendor/bin/phpcs --parallel=5 --standard=Magento2 --no-cache --extensions=php --warning-severity=0 --error-severity=1 --no-colors "${affected_file}"
            fi
          done
      - name: Check Dependency Injection
        run: php -dzend.enable_gc=0 -dmemory_limit=-1 bin/magento setup:di:compile --ansi