diff --git a/app/code/Webkul/Marketplace/Controller/Account/ResetPassword.php b/app/code/Webkul/Marketplace/Controller/Account/ResetPassword.php
new file mode 100644
index *********..5c2d200f1
--- /dev/null
+++ b/app/code/Webkul/Marketplace/Controller/Account/ResetPassword.php
@@ -0,0 +1,48 @@
+<?php
+declare(strict_types=1);
+
+namespace Webkul\Marketplace\Controller\Account;
+
+use Magento\Framework\App\Action\Action;
+use Magento\Framework\App\Action\Context;
+use Magento\Customer\Api\AccountManagementInterface;
+use Magento\Customer\Api\CustomerRepositoryInterface;
+use Magento\Customer\Model\Session as CustomerSession;
+use Magento\Customer\Model\AccountManagement;
+use Magento\Framework\Exception\NoSuchEntityException;
+
+class ResetPassword extends Action
+{
+    public function __construct(
+        Context $context,
+        private AccountManagementInterface $accountManagement,
+        private CustomerRepositoryInterface $customerRepository,
+        private CustomerSession $customerSession
+    ) {
+        parent::__construct($context);
+    }
+
+    public function execute()
+    {
+        try {
+            $customerId = (int) $this->customerSession->getId();
+            $email = $this->customerRepository->getById($customerId)->getEmail();
+
+            $this->accountManagement->initiatePasswordReset(
+                $email,
+                AccountManagement::EMAIL_RESET
+            );
+
+            $this->messageManager->addSuccessMessage(
+                __('We sent a password-reset link to %1.', $email)
+            );
+        } catch (NoSuchEntityException $e) {
+            $this->messageManager->addErrorMessage(__('Unable to find your customer account.'));
+        } catch (\Exception $e) {
+            $this->messageManager->addErrorMessage(__('We could not send the reset e-mail.'));
+        }
+
+        return $this->resultRedirectFactory->create()
+            ->setPath('marketplace/account/editprofile');
+    }
+}
diff --git a/app/code/Webkul/Marketplace/etc/db_schema.xml b/app/code/Webkul/Marketplace/etc/db_schema.xml
index f50eca8dc..fa4b2227b 100755
--- a/app/code/Webkul/Marketplace/etc/db_schema.xml
+++ b/app/code/Webkul/Marketplace/etc/db_schema.xml
@@ -265,6 +265,20 @@
         <column xsi:type="smallint" name="admin_notification" unsigned="true" padding="5" nullable="false" default="0" comment="Notification flag for admin"/>
         <column xsi:type="text" name="privacy_policy" nullable="true" comment="Privacy Policy"/>
         <column xsi:type="text" name="allowed_categories" nullable="false" comment="Allowed Categories Ids"/>
+        <column xsi:type="varchar" name="email" nullable="true" length="255" comment="e-mail"/>
+        <column xsi:type="varchar" name="business_url" nullable="true" length="255" comment="Seller’s public URL"/>
+        <column xsi:type="varchar" name="owner_name" nullable="true" length="255" comment="Seller’s public URL"/>
+        <column xsi:type="varchar"    name="business_country"  length="128"   nullable="true"/>
+        <column xsi:type="varchar" name="business_city"     length="128" nullable="true"/>
+        <column xsi:type="varchar" name="business_street"   length="255" nullable="true"/>
+        <column xsi:type="varchar" name="business_postcode" length="30"  nullable="true"/>
+        <column xsi:type="smallint" name="ship_same_as_business" default="0"/>
+        <column xsi:type="varchar"     name="ship_country"          length="128"  nullable="true"/>
+        <column xsi:type="varchar"  name="ship_city"             length="128" nullable="true"/>
+        <column xsi:type="varchar"  name="ship_street"           length="255" nullable="true"/>
+        <column xsi:type="varchar"  name="ship_postcode"         length="30"  nullable="true"/>
+        <column xsi:type="varchar" name="vendor_group" length="20"  nullable="true" comment="Retail / Sports / Club"/>
+        <column xsi:type="varchar" name="business_registration_number" length="128" nullable="true" comment="Company registration number"/>
         <constraint xsi:type="primary" referenceId="PRIMARY">
             <column name="entity_id"/>
         </constraint>
diff --git a/app/design/frontend/Pearl/weltpixel_custom/Webkul_Marketplace/templates/account/editprofile.phtml b/app/design/frontend/Pearl/weltpixel_custom/Webkul_Marketplace/templates/account/editprofile.phtml
index b04c00a50..d0f4fffa8 100644
--- a/app/design/frontend/Pearl/weltpixel_custom/Webkul_Marketplace/templates/account/editprofile.phtml
+++ b/app/design/frontend/Pearl/weltpixel_custom/Webkul_Marketplace/templates/account/editprofile.phtml
@@ -62,137 +62,11 @@ $countryflag = $block->getViewFileUrl('Webkul_Marketplace::images/country/countr
             </div>
             <?= $block->getBlockHtml('seller.formkey')?>
             <?= $block->getBlockHtml('formkey')?>
+
             <div class="field profile">
-                <label for="twitterid"><?= /* @noEscape */ __('Twitter ID') ?> </label>
-                <?php $checkedVal = "";
-                if ($partner['tw_active'] == 1) { $checkedVal = "checked='checked'";} ?>
-                <input type="checkbox" name="tw_active"
-                    value="1"
-                    title="<?= /* @noEscape */ __('Allow to Display Twitter Icon in Profile Page') ?>"
-                    <?= /* @noEscape */ $checkedVal ?>
-                    style="margin: 5px;">
-                <?php
-                if ($profile_hint_status && $helper->getProfileHintTw()) {?>
-                    <img src="<?= $escaper->escapeUrl($questIcon); ?>"
-                    class='questimg'
-                    title="<?= $escaper->escapeHtml($helper->getProfileHintTw()) ?>"/>
-                    <?php
-                } ?>
-                <div class="control">
-                    <input type="text" id="twitter_id" name="twitter_id"
-                    value="<?= $escaper->escapeHtml($partner['twitter_id']); ?>"
-                    title="twitterid" class="input-text" />
-                </div>
-            </div>
-            <div class="field profile">
-                <label for="facebookid"><?= /* @noEscape */ __('Facebook ID') ?> </label>
-                <?php $fbChecked = "";
-                if ($partner['fb_active'] == 1) { $fbChecked =  "checked='checked'";} ?>
-                <input type="checkbox" name="fb_active" value="1"
-                title="<?= /* @noEscape */ __('Allow to Display Facebook Icon in Profile Page') ?>"
-                <?= /* @noEscape */  $fbChecked ?>
-                style="margin: 5px;">
-                <?php
-                if ($profile_hint_status && $helper->getProfileHintFb()) {?>
-                    <img src="<?= $escaper->escapeUrl($questIcon); ?>" class='questimg'
-                    title="<?= $escaper->escapeHtml($helper->getProfileHintFb()) ?>"/>
-                    <?php
-                } ?>
-                <div class="control">
-                    <input type="text" id="facebook_id" name="facebook_id"
-                    value="<?= $escaper->escapeHtml($partner['facebook_id']); ?>"
-                    title="facebookid" class="input-text" />
-                </div>
-            </div>
-            <div class="field profile">
-                <label><?= /* @noEscape */ __('Instagram ID') ?> </label>
-                <?php $instaCheck = "";
-                if ($partner['instagram_active'] == 1) { $instaCheck = "checked='checked'";} ?>
-                <input type="checkbox" name="instagram_active" value="1"
-                title="<?= /* @noEscape */ __('Allow to Display Instagram Icon in Profile Page') ?>"
-                <?= /* @noEscape */ $instaCheck ?>
-                style="margin: 5px;">
-                <?php
-                if ($profile_hint_status && $helper->getProfileHintInsta()) {?>
-                    <img src="<?= $escaper->escapeUrl($questIcon); ?>"
-                    class='questimg'
-                    title="<?= $escaper->escapeHtml($helper->getProfileHintInsta()) ?>"/>
-                    <?php
-                } ?>
-                <div class="control">
-                    <input type="text" id="instagram_id"
-                    name="instagram_id"
-                    value="<?= $escaper->escapeHtml($partner['instagram_id']); ?>"
-                    class="input-text" />
-                </div>
-            </div>
-            <div class="field profile">
-                <label><?= /* @noEscape */ __('Youtube ID') ?> </label>
-                <?php $youtCheck = "";
-                if ($partner['youtube_active'] == 1) { $youtCheck = "checked='checked'";} ?>
-                <input type="checkbox" name="youtube_active" value="1"
-                title="<?= /* @noEscape */ __('Allow to Display Youtube Icon in Profile Page') ?>"
-                <?=  /* @noEscape */ $youtCheck;?>
-                style="margin: 5px;">
-                <?php
-                if ($profile_hint_status && $helper->getProfileHintYoutube()) {?>
-                    <img src="<?= $escaper->escapeUrl($questIcon); ?>"
-                    class='questimg'
-                    title="<?= $escaper->escapeHtml($helper->getProfileHintYoutube()) ?>"/>
-                    <?php
-                } ?>
-                <div class="control">
-                    <input type="text" id="youtube_id" name="youtube_id"
-                    value="<?= $escaper->escapeHtml($partner['youtube_id']); ?>"
-                    class="input-text" />
-                </div>
-            </div>
-            <div class="field profile">
-                <label><?= /* @noEscape */ __('Vimeo ID') ?> </label>
-                <?php $vimCheck = "";
-                if ($partner['vimeo_active'] == 1) { $vimCheck = "checked='checked'";} ?>
-                <input type="checkbox" name="vimeo_active" value="1"
-                title="<?= /* @noEscape */ __('Allow to Display Vimeo Icon in Profile Page') ?>"
-                <?= /* @noEscape */ $vimCheck ?>
-                style="margin: 5px;">
-                <?php
-                if ($profile_hint_status && $helper->getProfileHintVimeo()) {?>
-                    <img src="<?= $escaper->escapeUrl($questIcon); ?>"
-                    class='questimg'
-                    title="<?= $escaper->escapeHtml($helper->getProfileHintVimeo()) ?>"/>
-                    <?php
-                } ?>
-                <div class="control">
-                    <input type="text"
-                    id="vimeo_id" name="vimeo_id"
-                    value="<?= $escaper->escapeHtml($partner['vimeo_id']); ?>"
-                    class="input-text" />
-                </div>
-            </div>
-            <div class="field profile">
-                <label><?= /* @noEscape */ __('Pinterest ID') ?> </label>
-                <?php $pintCheck = "";
-                if ($partner['pinterest_active'] == 1) { $pintCheck = "checked='checked'";} ?>
-                <input type="checkbox"
-                name="pinterest_active" value="1"
-                title="<?= /* @noEscape */ __('Allow to Display Pinterest Icon in Profile Page') ?>"
-                <?= /* @noEscape */ $pintCheck ?>
-                style="margin: 5px;">
-                <?php
-                if ($profile_hint_status && $helper->getProfileHintPinterest()) {?>
-                    <img src="<?= $escaper->escapeUrl($questIcon); ?>"
-                    class='questimg'
-                    title="<?= $escaper->escapeHtml($helper->getProfileHintPinterest()) ?>"/>
-                    <?php
-                } ?>
-                <div class="control">
-                    <input type="text"
-                    id="pinterest_id" name="pinterest_id"
-                    value="<?= $escaper->escapeHtml($partner['pinterest_id']); ?>"
-                    class="input-text" />
-                </div>
-            </div>
-            <div class="field profile">
+
+                <h2><?= /* @noEscape */ __('Contact information') ?></h2>
+
                 <label><?= /* @noEscape */ __('Contact Number') ?> </label>
                 <?php
                 if ($profile_hint_status && $helper->getProfileHintCn()) {?>
@@ -214,9 +88,227 @@ $countryflag = $block->getViewFileUrl('Webkul_Marketplace::images/country/countr
                     class="input-text"
                     placeholder="<?= /* @noEscape */ $placeholderTxt ?>"/>
                 </div>
+                <div class="field profile">
+                    <label><?= /* @noEscape */ __('Email') ?></label>
+                    <div class="control">
+                        <input type="email"
+                               name="email"
+                               id="email"
+                               class="input-text"
+                               placeholder="<?= /* @noEscape */ __('Enter Email') ?>"
+                               value="<?= $escaper->escapeHtml($partner['email'] ?? '') ?>" />
+                    </div>
+                </div>
+                <div class="field profile">
+                    <label><?= /* @noEscape */ __('Business URL') ?></label>
+                    <div class="control">
+                        <input type="url"
+                               name="business_url"
+                               id="business_url"
+                               class="input-text"
+                               placeholder="https://example.com"
+                               value="<?= $escaper->escapeHtml($partner['business_url'] ?? '') ?>" />
+                    </div>
+                </div>
+
+                <h2><?= /* @noEscape */ __('Business details') ?></h2>
+                <div class="field profile">
+                    <label for="shoptitle"><?= /* @noEscape */ __('Business Name') ?></label>
+                    <?php
+                    if ($profile_hint_status && $helper->getProfileHintShop()) {?>
+                        <img src="<?= $escaper->escapeUrl($questIcon); ?>"
+                             class='questimg'
+                             title="<?= $escaper->escapeHtml($helper->getProfileHintShop()) ?>"/>
+                        <?php
+                    } ?>
+                    <div class="control">
+                        <input type="text" id="shop_title"
+                               name="shop_title"
+                               value="<?= $escaper->escapeHtml($partner['shop_title']); ?>"
+                               title="<?= /* @noEscape */ __('Business Name')?>"
+                               placeholder="<?= /* @noEscape */ __('Enter Business Name') ?>"
+                               class="input-text" />
+                    </div>
+                </div>
+                <div class="field profile">
+                    <label><?= /* @noEscape */ __('Owner Name') ?></label>
+                    <div class="control">
+                        <input type="text"
+                               name="owner_name"
+                               id="owner_name"
+                               class="input-text"
+                               placeholder="<?= /* @noEscape */ __('Enter Owner Name') ?>"
+                               value="<?= $escaper->escapeHtml($partner['owner_name'] ?? '') ?>" />
+                    </div>
+                </div>
+
+                <div class="fieldset-sublegend"><?= __('Business Address') ?></div>
+
+                <div class="field profile required">
+                    <label for="business_country"><?= __('Country') ?></label>
+                    <div class="control">
+                        <select name="business_country"
+                                id="business_country"
+                                class="select required-entry">
+                            <option value="" selected="selected" disabled="disabled">
+                                <?= /* @noEscape */ __('Select Country')?>
+                            </option>
+                            <?php foreach ($block->getCountryOptionArray() as $country) {?>
+                                <option <?php
+                                if ($country['value']!='') {
+                                    $cSelect = $partner['business_country']==$country['value']?"selected='selected'":""; ?>
+                                    value="<?= $escaper->escapeHtml($country['value']); ?>" <?= /* @noEscape */ $cSelect?>>
+                                    <?= $escaper->escapeHtml($country['label']);?>
+                                    </option>
+                                    <?php
+                                }
+                            } ?>
+                        </select>
+                    </div>
+                </div>
+
+                <div class="field profile required">
+                    <label for="business_city"><?= __('City') ?></label>
+                    <div class="control">
+                        <input type="text"
+                               name="business_city"
+                               id="business_city"
+                               class="input-text required-entry"
+                               value="<?= $escaper->escapeHtml($partner['business_city'] ?? '') ?>"/>
+                    </div>
+                </div>
+
+                <div class="field profile required">
+                    <label for="business_street"><?= __('Street') ?></label>
+                    <div class="control">
+                        <input type="text"
+                               name="business_street"
+                               id="business_street"
+                               class="input-text required-entry"
+                               value="<?= $escaper->escapeHtml($partner['business_street'] ?? '') ?>"/>
+                    </div>
+                </div>
+
+                <div class="field profile required">
+                    <label for="business_postcode"><?= __('Postal Code') ?></label>
+                    <div class="control">
+                        <input type="text"
+                               name="business_postcode"
+                               id="business_postcode"
+                               class="input-text required-entry validate-alphanum"
+                               value="<?= $escaper->escapeHtml($partner['business_postcode'] ?? '') ?>"/>
+                    </div>
+                </div>
+
+                <div class="fieldset-sublegend"><?= __('Shipping From Address') ?></div>
+
+                <div class="field profile">
+                    <label class="label">
+                        <input type="hidden"
+                               name="ship_same_as_business"
+                               value="0" />
+
+                        <input type="checkbox"
+                               id="ship_same_as_business"
+                               name="ship_same_as_business"
+                               value="1"
+                            <?= ($partner['ship_same_as_business'] ?? 0) ? 'checked' : '' ?> />
+
+                        <?= __('Same as Business Address') ?>
+                    </label>
+                </div>
+
+
+                <div class="field profile required ship-block">
+                    <label for="ship_country"><?= __('Country') ?></label>
+                    <div class="control">
+                        <select name="ship_country"
+                                id="ship_country"
+                                class="select">
+                            <option value="" selected="selected" disabled="disabled">
+                                <?= /* @noEscape */ __('Select Country')?>
+                            </option>
+                            <?php foreach ($block->getCountryOptionArray() as $country) {?>
+                                <option <?php
+                                if ($country['value']!='') {
+                                    $cSelect = $partner['ship_country']==$country['value']?"selected='selected'":""; ?>
+                                    value="<?= $escaper->escapeHtml($country['value']); ?>" <?= /* @noEscape */ $cSelect?>>
+                                    <?= $escaper->escapeHtml($country['label']);?>
+                                    </option>
+                                    <?php
+                                }
+                            } ?>
+                        </select>
+                    </div>
+                </div>
+
+                <div class="field profile required ship-block">
+                    <label for="ship_city"><?= __('City') ?></label>
+                    <div class="control">
+                        <input type="text" name="ship_city"
+                               id="ship_city" class="input-text"
+                               value="<?= $escaper->escapeHtml($partner['ship_city'] ?? '') ?>"/>
+                    </div>
+                </div>
+
+                <div class="field profile required ship-block">
+                    <label for="ship_street"><?= __('Street') ?></label>
+                    <div class="control">
+                        <input type="text" name="ship_street"
+                               id="ship_street" class="input-text"
+                               value="<?= $escaper->escapeHtml($partner['ship_street'] ?? '') ?>"/>
+                    </div>
+                </div>
+
+                <div class="field profile required ship-block">
+                    <label for="ship_postcode"><?= __('Postal Code') ?></label>
+                    <div class="control">
+                        <input type="text" name="ship_postcode"
+                               id="ship_postcode"
+                               class="input-text validate-alphanum"
+                               value="<?= $escaper->escapeHtml($partner['ship_postcode'] ?? '') ?>"/>
+                    </div>
+                </div>
+
+                <h2><?= /* @noEscape */ __('Business classification') ?></h2>
+
+                <div class="field profile required">
+                    <label for="vendor_group"><?= __('Vendor Group') ?></label>
+                    <div class="control">
+                        <select name="vendor_group"
+                                id="vendor_group"
+                                class="select required-entry">
+                            <option value="" disabled <?= empty($partner['vendor_group']) ? 'selected' : '' ?>>
+                                <?= __('Select Group') ?>
+                            </option>
+                            <option value="Retail" <?= ($partner['vendor_group'] ?? '') == 'Retail' ? 'selected' : '' ?>>
+                                <?= __('Retail') ?>
+                            </option>
+                            <option value="Sports" <?= ($partner['vendor_group'] ?? '') == 'Sports' ? 'selected' : '' ?>>
+                                <?= __('Sports') ?>
+                            </option>
+                            <option value="Club" <?= ($partner['vendor_group'] ?? '') == 'Club' ? 'selected' : '' ?>>
+                                <?= __('Club') ?>
+                            </option>
+                        </select>
+                    </div>
+                </div>
+
+                <div class="field profile required">
+                    <label for="business_registration_number"><?= __('Business Registration Number') ?></label>
+                    <div class="control">
+                        <input type="text"
+                               name="business_registration_number"
+                               id="business_registration_number"
+                               class="input-text"
+                               placeholder="<?= /* @noEscape */ __('Enter Business Registration Number') ?>"
+                               value="<?= $escaper->escapeHtml($partner['business_registration_number'] ?? '') ?>" />
+                    </div>
+                </div>
+
             </div>
             <div class="field profile">
-                <label><?= /* @noEscape */ __('Tax/VAT Number') ?> </label>
+                <label><?= /* @noEscape */ __('VAT Number') ?> </label>
                 <?php
                 if ($profile_hint_status && $helper->getProfileHintTax()) {?>
                     <img src="<?= $escaper->escapeUrl($questIcon); ?>"
@@ -233,6 +325,25 @@ $countryflag = $block->getViewFileUrl('Webkul_Marketplace::images/country/countr
                     placeholder="<?= /* @noEscape */ __('Enter Tax or VAT number') ?>"/>
                 </div>
             </div>
+
+
+            <h2><?= __('Authentication') ?></h2>
+
+            <div class="field profile">
+                <button type="button"
+                        id="send-reset-link"
+                        class="button"
+                    <span><?= __('Reset Password') ?></span>
+                </button>
+            </div>
+
+            <script type="text/javascript">
+                require(['jquery'], function ($) {
+                    $('#send-reset-link').on('click', function () {
+                        window.location = '<?= $block->getUrl("marketplace/account/resetPassword"); ?>';
+                    });
+                });
+            </script>
             <?php
             if ($helper->getActiveColorPicker()) {?>
                 <div class="field profile">
@@ -265,156 +376,8 @@ $countryflag = $block->getViewFileUrl('Webkul_Marketplace::images/country/countr
                 </div>
                 <?php
             } ?>
-            <div class="field profile">
-                <label for="shoptitle"><?= /* @noEscape */ __('Shop Title') ?></label>
-                <?php
-                if ($profile_hint_status && $helper->getProfileHintShop()) {?>
-                    <img src="<?= $escaper->escapeUrl($questIcon); ?>"
-                    class='questimg'
-                    title="<?= $escaper->escapeHtml($helper->getProfileHintShop()) ?>"/>
-                    <?php
-                } ?>
-                <div class="control">
-                    <input type="text" id="shop_title"
-                    name="shop_title"
-                    value="<?= $escaper->escapeHtml($partner['shop_title']); ?>"
-                    title="<?= /* @noEscape */ __('Shop Title')?>"
-                    class="input-text" />
-                </div>
-            </div>
-            <div class="field profile">
-                <label for="banner-pic"> <?= /* @noEscape */ __('Company Banner') ?> </label>
-                <?php
-                if ($profile_hint_status && $helper->getProfileHintBanner()) {?>
-                    <img src="<?= $escaper->escapeUrl($questIcon); ?>"
-                    class='questimg'
-                    title="<?= $escaper->escapeHtml($helper->getProfileHintBanner()) ?>"/>
-                    <?php
-                } ?>
-                <div class="control">
-                    <input type="file" id="banner-pic" name="banner_pic"  title="Banner Pic" class="banner" size="26" />
-                    <div class="profileimage-set">
-                          <div class="setimage">
-                            <img class="wk-banner"
-                            alt=" <?= /* @noEscape */ __('no image') ?>"
-                            src="<?= $escaper->escapeUrl($helper->getMediaUrl().'avatar/'.$partner['banner_pic']); ?>"/>
-                            <?php
-                            $bannerpic = $partner['banner_pic'];
-                            $logopic = $partner['logo_pic'];
-                            if ($bannerpic && !$partner['is_default_banner']) {
-                                ?>
-                                <span class="wk-profileimagedelete"
-                                    title="<?= /* @noEscape */ __('Delete')?>">
-                                    <img src="<?= $escaper->escapeUrl($deleteIcon); ?>"
-                                    alt="<?= /* @noEscape */ __('Delete Image')?>"
-                                    title="<?= /* @noEscape */ __('Delete Image')?>"/>
-                                </span>
-                                <?php
-                            }?>
-                        </div>
-                    </div>
-                </div>
-            </div>
-            <div class="field profile">
-                <label for="logo-pic"> <?= /* @noEscape */ __('Company Logo') ?> </label>
-                <?php
-                if ($profile_hint_status && $helper->getProfileHintLogo()) {?>
-                    <img src="<?= $escaper->escapeUrl($questIcon); ?>"
-                    class='questimg'
-                    title="<?= $escaper->escapeHtml($helper->getProfileHintLogo()) ?>"/>
-                    <?php
-                } ?>
-                <div class="control">
-                    <input type="file" id="logo-pic"
-                    alt="no image" name="logo_pic"
-                    title="<?= /* @noEscape */ __('Seller Logo')?>"
-                    class="banner" size="26"/>
-                    <div class="logoimage-set">
-                        <div class="setimage">
-                            <img class="prev-img"
-                            src="<?= $escaper
-                            ->escapeUrl($helper->getMediaUrl().'avatar/'.$partner['logo_pic']); ?>"/>
-                            <?php
-                            if ($logopic && !$partner['is_default_logo']) {
-                                ?>
-                                <span class="wk-logoimagedelete"
-                                    title="<?= /* @noEscape */ __('Delete')?>">
-                                    <img src="<?= $escaper
-                                    ->escapeUrl($deleteIcon); ?>"
-                                    alt="<?= /* @noEscape */ __('Delete Image')?>"
-                                    title="<?= /* @noEscape */ __('Delete Image')?>"/>
-                                </span>
-                                <?php
-                            }?>
-                        </div>
-                    </div>
-                </div>
-            </div>
-            <div class="field profile">
-                <label for="company_locality"><?= /* @noEscape */ __('Company Locality') ?></label>
-                <?php
-                if ($profile_hint_status && $helper->getProfileHintLoc()) {?>
-                    <img src="<?= $escaper->escapeUrl($questIcon); ?>"   class='questimg'
-                    title="<?= $escaper->escapeHtml($helper->getProfileHintLoc()) ?>"/>
-                    <?php
-                } ?>
-                <div class="control">
-                    <input type="text" id="company_locality"
-                    name="company_locality"
-                    value="<?= $escaper->escapeHtml($partner['company_locality']); ?>"
-                    title="company_locality"
-                    class="input-text" />
-                </div>
-            </div>
-            <div class="field profile">
-                <label for="company_description"><?= /* @noEscape */ __('Company Description') ?></label>
-                <?php
-                if ($profile_hint_status && $helper->getProfileHintDesc()) {?>
-                    <img src="<?= $escaper->escapeUrl($questIcon); ?>"
-                    class='questimg'
-                    title="<?= $escaper->escapeHtml($helper->getProfileHintDesc()) ?>"/>
-                    <?php
-                } ?>
-                <?php
-                $cDesc = $partner['company_description'];
-                if (!empty($partner['company_description'])) {
-                    $cDesc = trim($partner['company_description']);
-                }
-                ?>
-                <div class="control wk-border-box-sizing">
-                    <textarea type="text" id="company_description"
-                    name="company_description" title="company_description"
-                    class="input-text compdesi"
-                    ><?= /* @noEscape */ $cDesc; ?> </textarea>
-                    <?php if ($helper->isWysiwygEnabled()): ?>
-                        <script>
-                            require([
-                                "jquery",
-                                "mage/translate",
-                                "mage/adminhtml/events",
-                                "mage/adminhtml/wysiwyg/tiny_mce/setup"
-                            ], function(jQuery) {
-                                wysiwygCompanyDescription = new wysiwygSetup("company_description", {
-                                    "width" : "100%",
-                                    "height" : "200px",
-                                    "plugins" : [{"name":"image"}],
-                                    "tinymce4" : {
-                                        "toolbar":"formatselect | bold italic underline | "+
-                                        "alignleft aligncenter alignright |" +
-                                        "bullist numlist |"+
-                                        "link table charmap","plugins":"advlist "+
-                                        "autolink lists link charmap media noneditable table "+
-                                        "contextmenu paste code help table",
-                                    },
-                                    files_browser_window_url: "<?= $escaper
-                                    ->escapeUrl($block->getWysiwygUrl());?>"
-                                });
-                                wysiwygCompanyDescription.setup("exact");
-                            });
-                        </script>
-                    <?php endif; ?>
-                </div>
-            </div>
+
+
             <?php
             if ($helper->getSellerPolicyApproval()) {?>
                 <div class="field profile">
@@ -547,82 +510,6 @@ $countryflag = $block->getViewFileUrl('Webkul_Marketplace::images/country/countr
                 <?php
             } ?>
 
-            <div class="field profile">
-                <label for="country_pic"> <?= /* @noEscape */ __('Country') ?> </label>
-                <?php
-                if ($profile_hint_status && $helper->getProfileHintCountry()) {?>
-                    <img src="<?= $escaper->escapeUrl($questIcon); ?>"
-                    class='questimg'
-                    title="<?= $escaper->escapeHtml($helper->getProfileHintCountry()) ?>"/>
-                    <?php
-                } ?>
-                <div class="control">
-                    <select name="country_pic" id="country-pic">
-                        <option value="" selected="selected" disabled="disabled">
-                            <?= /* @noEscape */ __('Select Country')?>
-                        </option>
-                    <?php foreach ($block->getCountryOptionArray() as $country) {?>
-                        <option <?php
-                        if ($country['value']!='') {
-                            $cSelect = $partner['country_pic']==$country['value']?"selected='selected'":""; ?>
-                            value="<?= $escaper->escapeHtml($country['value']); ?>" <?= /* @noEscape */ $cSelect?>>
-                            <?= $escaper->escapeHtml($country['label']);?>
-                            </option>
-                            <?php
-                        }
-                    } ?>
-                    </select>
-                    <img class="country_img_prev" alt="no image"
-                    src="<?= /* @noEscape */ $countryflag."/".strtoupper(
-                        $partner['country_pic']==""?"xx":$partner['country_pic']
-                    ).".png"; ?>"/>
-                </div>
-            </div>
-            <div class="field profile">
-                <label for="meta_keywords"><?= /* @noEscape */ __('Meta Keywords') ?></label>
-                <label class="control-notification">
-                (<?= /* @noEscape */ __("Enter Meta Keywords Comma(',') Separated.."); ?>)
-                </label>
-                <?php
-                if ($profile_hint_status && $helper->getProfileHintMeta()) {?>
-                    <img src="<?= $escaper->escapeUrl($questIcon); ?>"
-                    class='questimg'
-                    title="<?= $escaper->escapeHtml($helper->getProfileHintMeta()) ?>"/>
-                    <?php
-                } ?>
-                 <?php
-                    $metaKey = $partner['meta_keyword'];
-                    if (!empty($partner['meta_keyword'])) {
-                        $metaKey = trim($partner['meta_keyword']);
-                    }
-                    ?>
-                <div class="control">
-                    <textarea type="text" id="meta_keywords" name="meta_keyword" title="Meta Keyword"
-                    class="input-text compdesi"
-                    ><?= /* @noEscape */ $metaKey; ?></textarea>
-                </div>
-            </div>
-            <div class="field profile">
-                <label for="meta_description"><?= /* @noEscape */ __('Meta Description') ?></label>
-                <?php
-                if ($profile_hint_status && $helper->getProfileHintMetaDesc()) {?>
-                    <img src="<?= $escaper->escapeUrl($questIcon); ?>"
-                    class='questimg'
-                    title="<?= $escaper->escapeHtml($helper->getProfileHintMetaDesc()) ?>"/>
-                    <?php
-                } ?>
-                 <?php
-                    $metaDesc = $partner['meta_description'];
-                    if (!empty($partner['meta_description'])) {
-                        $metaDesc = trim($partner['meta_description']);
-                    }
-                    ?>
-                <div class="control">
-                <textarea type="text" id="meta_description"
-                    name="meta_description" title="Meta Description" class="input-text compdesi"
-                    ><?= /* @noEscape */ $metaDesc ?></textarea>
-                </div>
-            </div>
         </fieldset>
     </div>
 </form>
@@ -735,49 +622,6 @@ if ($helper->getUrlRewrite()) { ?>
 }
 ?>
 <br/><br/>
-<?php
-$savePaymentInfoUrl = $block->getUrl(
-    'marketplace/account/savePaymentInfo',
-    ["_secure" => $block->getRequest()->isSecure()]
-);
-?>
-<form action="<?= $escaper->escapeUrl($savePaymentInfoUrl) ?>"
-    enctype="multipart/form-data" method="post"
-    data-role="form-payment-validate" data-mage-init='{"validation":{}}'>
-    <div class="wk-mp-page-title page-title">
-        <h2><?= /* @noEscape */ __('Edit Payment Information') ?></h2>
-        <button class="button wk-mp-btn"
-        title="<?= /* @noEscape */ __('Save Payment') ?>" type="submit">
-            <span><span><?= /* @noEscape */ __('Save Payment') ?></span></span>
-        </button>
-    </div>
-    <?= $block->getBlockHtml('seller.formkey')?>
-    <?= $block->getBlockHtml('formkey')?>
-    <div class="wk-mp-design">
-        <fieldset class="fieldset info wk-mp-fieldset">
-            <legend class="legend">
-            <span><?= /* @noEscape */ __('Payment Information') ?></span>
-            </legend>
-            <div class="field">
-                <label><?= /* @noEscape */ __('Payment Details') ?></label>
-                <?php
-                if ($profile_hint_status && $helper->getProfileHintBank()) {?>
-                    <img src="<?= $escaper->escapeUrl($questIcon); ?>"
-                    class='questimg'
-                    title="<?= $escaper->escapeHtml($helper->getProfileHintBank()) ?>"/>
-                    <?php
-                } ?>
-                <div class="control">
-                    <textarea class="input-text" name="payment_source"
-                    id="payment-source" title="payment source"
-                    cols="1" rows="3" ><?= /* @noEscape */ $partner['payment_source']; ?></textarea>
-
-
-                </div>
-            </div>
-        </fieldset>
-    </div>
-</form>
 <br/><br/>
 <?php if ($helper->getMinOrderSettings()): ?>
     <?php
@@ -854,54 +698,9 @@ $lowStockUrl = $block->getUrl(
 </form>
 <br/><br/>
 
-<?php if ($helper->getAnalyticStatus()): ?>
-    <?php
-    $saveAnalyticDataUrl = $block->getUrl(
-        'marketplace/account/saveAnalyticData',
-        ["_secure" => $block->getRequest()->isSecure()]
-    );
-    ?>
-    <form action="<?= $escaper->escapeUrl($saveAnalyticDataUrl) ?>"
-    method="post" data-mage-init='{"validation":{}}'>
-        <div class="wk-mp-page-title page-title">
-            <h2><?= /* @noEscape */ __('Set Google Analytic Details') ?></h2>
-            <button class="button wk-mp-btn"
-            title="<?= /* @noEscape */ __('Save') ?>" type="submit">
-                <span><span><?= /* @noEscape */ __('Save') ?></span></span>
-            </button>
-        </div>
-        <?= $block->getBlockHtml('seller.formkey')?>
-        <?= $block->getBlockHtml('formkey')?>
-        <div class="wk-mp-design">
-            <fieldset class="fieldset info wk-mp-fieldset">
-                <div class="field required">
-                    <label class="label"><?= /* @noEscape */ __('Google Analytic Id') ?></label>
-                    <div class="control">
-                        <input type="text"
-                        class="input-text required-entry"
-                        name="analytic_id" title="Google Analytic Id"
-                        value ="<?= $escaper->escapeHtml($block->getAnalyticId()) ?>"
-                        id="analytic_id" />
-                    </div>
-                </div>
-            </fieldset>
-        </div>
-    </form>
-    <br/><br/>
-<?php endif; ?>
+
 <?= $block->getChildHtml(); ?>
-<div class="field profile wk-profile-links-container">
-    <div class="wk-profile-links">
-        <a class="btn-primary"
-        href="<?= /* @noEscape */ $helper->getRewriteUrl($profileUrl); ?>"
-        target="_blank"><?= /* @noEscape */ __('View Profile') ?></a>
-    </div>
-    <div class="wk-profile-links">
-        <a class="btn-primary"
-            href="<?= /* @noEscape */ $helper->getRewriteUrl($collectionUrl); ?>"
-             target="_blank"><?= /* @noEscape */ __('View Collection') ?></a>
-    </div>
-</div>
+
 <div class="buttons-set">
     <p class="back-link">
         <a href="javascript:;"
@@ -948,3 +747,41 @@ $serializedFormData = $viewModel->getJsonHelper()->jsonEncode($formData);
         }
     }
 </script>
+<script type="text/javascript">
+    require(['jquery'], function ($) {
+        'use strict';
+
+        var $same   = $('#ship_same_as_business'),
+            $block  = $('.ship-block'),       
+            fields  = {
+                country:  ['#business_country',  '#ship_country'],
+                city:     ['#business_city',     '#ship_city'],
+                street:   ['#business_street',   '#ship_street'],
+                post:     ['#business_postcode', '#ship_postcode']
+            };
+
+        function toggleShipping() {
+            var same = $same.prop('checked');
+
+            $block.toggle(!same);
+
+            $block.find('input, select')
+                .toggleClass('required-entry', !same);
+
+            if (same) {
+                $.each(fields, function (_, pair) {
+                    $(pair[1]).val($(pair[0]).val());
+                });
+            }
+        }
+
+        toggleShipping();
+        $same.on('change', toggleShipping);
+
+        $('#business_country').on('change', function () {
+            if ($same.prop('checked')) {
+                $('#ship_country').val(this.value);
+            }
+        });
+    });
+</script>
\ No newline at end of file
