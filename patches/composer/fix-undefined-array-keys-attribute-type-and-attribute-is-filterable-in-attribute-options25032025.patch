diff --git a/vendor/magento/module-catalog-graph-ql/DataProvider/Product/LayeredNavigation/Builder/Attribute.php b/vendor/magento/module-catalog-graph-ql/DataProvider/Product/LayeredNavigation/Builder/Attribute.php
index aca276f27..6e65fb1a0 100644
--- a/vendor/magento/module-catalog-graph-ql/DataProvider/Product/LayeredNavigation/Builder/Attribute.php
+++ b/vendor/magento/module-catalog-graph-ql/DataProvider/Product/LayeredNavigation/Builder/Attribute.php
@@ -97,11 +97,11 @@ class Attribute implements LayerBuilderInterface
                 $attribute['attribute_code'] ?? $bucketName,
                 isset($attribute['position']) ? $attribute['position'] : null
             );
-            $optionLabels = $attribute['attribute_type'] === 'boolean'
+            $optionLabels = (isset($attribute['attribute_type']) && $attribute['attribute_type'] === 'boolean')
                 ? $this->YesNo->toArray()
                 : $attribute['options'] ?? [];
             $result[$bucketName]['options'] = $this->getSortedOptions($bucket, $optionLabels);
-            if (self::ATTRIBUTE_OPTIONS_ONLY_WITH_RESULTS === $attribute['is_filterable']) {
+            if (isset($attribute['is_filterable']) && self::ATTRIBUTE_OPTIONS_ONLY_WITH_RESULTS === $attribute['is_filterable']) {
                 $result[$bucketName]['options'] = array_filter(
                     $result[$bucketName]['options'],
                     fn ($option) => $option['count']
