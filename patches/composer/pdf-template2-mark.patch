diff --git a/vendor/stripe/module-payments/view/base/templates/paymentInfo/element.phtml b/vendor/stripe/module-payments/view/base/templates/paymentInfo/element.phtml
--- a/vendor/stripe/module-payments/view/base/templates/paymentInfo/element.phtml
+++ b/vendor/stripe/module-payments/view/base/templates/paymentInfo/element.phtml	(date 1745478928218)
@@ -1,12 +1,18 @@
 <?php if (!empty($block->getPaymentMethodCode())): ?>
-<div style="line-height: 40px">
-    <?php if ($block->getPaymentMethodIconUrl()): ?>
-        <img height="24px" style="max-height: 24px" src="<?= $block->getPaymentMethodIconUrl("png"); //phpcs:ignore ?>">
-    <?php endif; ?>
-    <?php if ($block->getPaymentMethodName()): ?>
-        <span style="display: inline-block; position: relative; top: -7px; left: 5px;"><?= $block->getPaymentMethodName(true); //phpcs:ignore ?></span>
-    <?php endif; ?>
-</div>
+    <?php if (!$block->getPdfOverwrite()): ?>
+        <div style="line-height: 40px">
+            <?php if ($block->getPaymentMethodIconUrl()): ?>
+                <img height="24px" style="max-height: 24px"
+                     src="<?= $block->getPaymentMethodIconUrl("png"); //phpcs:ignore  ?>">
+            <?php endif; ?>
+            <?php if ($block->getPaymentMethodName()): ?>
+                <span
+                    style="display: inline-block; position: relative; top: -7px; left: 5px;"><?= $block->getPaymentMethodName(true); //phpcs:ignore  ?></span>
+            <?php endif; ?>
+        </div>
+    <?php else: ?>
+        <?= $block->getPaymentMethodName(true); //phpcs:ignore  ?>
+    <?php endif; ?>
 <?php endif; ?>

 <?php if ($block->getVoucherLink()): ?>
