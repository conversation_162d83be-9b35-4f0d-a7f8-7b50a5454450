--- a/Model/Rma/RequestRma.php	2024-09-26 19:52:40
+++ b/Model/Rma/RequestRma.php	2025-05-13 16:52:14
@@ -16,6 +16,7 @@
 use Magento\Rma\Api\Data\RmaInterface;
 use Magento\Rma\Api\RmaRepositoryInterface;
 use Magento\Sales\Api\OrderRepositoryInterface;
+use Psr\Log\LoggerInterface;

 /**
  * Request RMA executor
@@ -45,7 +46,8 @@
     public function __construct(
         OrderRepositoryInterface $orderRepository,
         RmaRepositoryInterface $rmaRepository,
-        Builder $builder
+        Builder $builder,
+        private readonly LoggerInterface $logger
     ) {
         $this->rmaRepository = $rmaRepository;
         $this->orderRepository = $orderRepository;
@@ -79,6 +81,13 @@
             $rma = $this->builder->build($order, $rmaData);
             $rma = $this->rmaRepository->save($rma);
         } catch (CouldNotSaveException $e) {
+            $this->logger->warning(
+                '[RmaCreate] Unable to save rma',
+                [
+                    'message' => $e->getMessage(),
+                ]
+            );
+
             throw new GraphQlNoSuchEntityException(__('Something went wrong while processing the request.'));
         }

