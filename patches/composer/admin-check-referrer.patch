--- a/App/Action/Plugin/Authentication.php	2025-04-16 22:13:58
+++ b/App/Action/Plugin/Authentication.php	2025-04-16 22:11:08
@@ -154,18 +154,30 @@
     protected function _processNotLoggedInUser(\Magento\Framework\App\RequestInterface $request)
     {
         $isRedirectNeeded = false;
-        if ($request->getPost('login')) {
-            if ($this->formKeyValidator->validate($request)) {
-                if ($this->_performLogin($request)) {
-                    $isRedirectNeeded = $this->_redirectIfNeededAfterLogin($request);
-                }
-            } else {
+        $httpReferrer = $request->getServer('HTTP_REFERER');
+        $baseUrl = $this->_url->getBaseUrl();
+
+        if (empty($httpReferrer) || !str_contains($httpReferrer, $baseUrl)) {
+            if (!$this->formKeyValidator->validate($request)) {
                 $this->_actionFlag->set('', \Magento\Framework\App\ActionInterface::FLAG_NO_DISPATCH, true);
-                $this->_response->setRedirect($this->_url->getCurrentUrl());
-                $this->messageManager->addErrorMessage(__('Invalid Form Key. Please refresh the page.'));
-                $isRedirectNeeded = true;
+                $this->_response->setHttpResponseCode(404);
+                return;
             }
+        } else {
+            if ($request->getPost('login')) {
+                if ($this->formKeyValidator->validate($request)) {
+                    if ($this->_performLogin($request)) {
+                        $isRedirectNeeded = $this->_redirectIfNeededAfterLogin($request);
+                    }
+                } else {
+                    $this->_actionFlag->set('', \Magento\Framework\App\ActionInterface::FLAG_NO_DISPATCH, true);
+                    $this->_response->setRedirect($this->_url->getCurrentUrl());
+                    $this->messageManager->addErrorMessage(__('Invalid Form Key. Please refresh the page.'));
+                    $isRedirectNeeded = true;
+                }
+            }
         }
+
         if (!$isRedirectNeeded && !$request->isForwarded()) {
             if ($request->getParam('isIframe')) {
                 $request->setForwarded(true)
