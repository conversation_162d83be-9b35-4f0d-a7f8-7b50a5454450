diff --git a/vendor/magento/module-page-cache/view/frontend/web/js/form-key-provider.js b/vendor/magento/module-page-cache/view/frontend/web/js/form-key-provider-fix.js
--- a/vendor/magento/module-page-cache/view/frontend/web/js/form-key-provider.js	2025-05-07 14:30:54
+++ b/vendor/magento/module-page-cache/view/frontend/web/js/form-key-provider-fix.js	2025-05-07 14:34:41
@@ -20,16 +20,18 @@
                 date = new Date(),
                 cookiesConfig = window.cookiesConfig || {},
                 isSecure = !!cookiesConfig.secure,
-                samesite = cookiesConfig.samesite || 'lax';
+                samesite = cookiesConfig.samesite || 'lax',
+                domain = cookiesConfig.domain || window.location.hostname;
 
             date.setTime(date.getTime() + 86400000);
             expires = '; expires=' + date.toUTCString();
             secure = isSecure ? '; secure' : '';
             samesite = '; samesite=' + samesite;
-
+            if (domain) {
+                expires += '; domain=' + domain;
+            }
             document.cookie = 'form_key=' + (value || '') + expires + secure + '; path=/' + samesite;
         }
-
         /**
          * Retrieves form key from cookie
          * @private
