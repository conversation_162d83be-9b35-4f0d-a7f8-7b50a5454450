diff --git a/vendor/stripe/module-payments/view/base/templates/paymentInfo/checkout.phtml b/vendor/stripe/module-payments/view/base/templates/paymentInfo/checkout.phtml
--- a/vendor/stripe/module-payments/view/base/templates/paymentInfo/checkout.phtml
+++ b/vendor/stripe/module-payments/view/base/templates/paymentInfo/checkout.phtml	(date 1743965157271)
@@ -1,10 +1,16 @@
 <?php if (!empty($block->getPaymentMethodCode())): ?>
-<div style="line-height: 40px">
-    <?php if ($block->getPaymentMethodIconUrl()): ?>
-        <img height="24px" style="max-height: 24px" src="<?= $block->getPaymentMethodIconUrl("png"); //phpcs:ignore ?>">
-    <?php endif; ?>
-    <?php if ($block->getPaymentMethodName()): ?>
-        <span style="display: inline-block; position: relative; top: -7px; left: 5px;"><?= $block->getPaymentMethodName(true); //phpcs:ignore ?></span>
-    <?php endif; ?>
-</div>
+    <?php if (!$block->getPdfOverwrite()): ?>
+        <div style="line-height: 40px">
+            <?php if ($block->getPaymentMethodIconUrl()): ?>
+                <img height="24px" style="max-height: 24px"
+                     src="<?= $block->getPaymentMethodIconUrl("png"); //phpcs:ignore  ?>">
+            <?php endif; ?>
+            <?php if ($block->getPaymentMethodName()): ?>
+                <span
+                    style="display: inline-block; position: relative; top: -7px; left: 5px;"><?= $block->getPaymentMethodName(true); //phpcs:ignore  ?></span>
+            <?php endif; ?>
+        </div>
+    <?php else: ?>
+        <?= $block->getPaymentMethodName(true); //phpcs:ignore  ?>
+    <?php endif; ?>
 <?php endif; ?>
