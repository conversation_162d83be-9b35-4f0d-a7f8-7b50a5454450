--- a/Model/Resolver/OrderTotal.php	2024-04-03 19:20:24
+++ b/Model/Resolver/OrderTotal.php	2025-04-16 15:05:24
@@ -77,8 +77,11 @@
         $appliedTaxes = $extensionAttributes->getAppliedTaxes() ?? [];
         $allAppliedTaxOnOrders = [];
         foreach ($appliedTaxes as $taxIndex => $appliedTaxesData) {
+            $rates = $appliedTaxesData->getExtensionAttributes()?->getRates() ?? null;
+            $title = $appliedTaxesData->getDataByKey('title') ?: ($rates ? current($rates)->getTitle() : null);
+
             $allAppliedTaxOnOrders[$taxIndex] = [
-                'title' => $appliedTaxesData->getDataByKey('title'),
+                'title' => $title,
                 'percent' => $appliedTaxesData->getDataByKey('percent'),
                 'amount' => $appliedTaxesData->getDataByKey('amount'),
             ];
@@ -99,7 +102,7 @@
         foreach ($allAppliedTaxOnOrders as $appliedTaxes) {
             $appliedTaxesArray = [
                 'rate' => $appliedTaxes['percent'] ?? 0,
-                'title' => $appliedTaxes['title'] ?? null,
+                'title' => $appliedTaxes['title'] ?? '-',
                 'amount' => [
                     'value' => $appliedTaxes['amount'] ?? 0,
                     'currency' => $order->getOrderCurrencyCode()
