--- a/Model/AdminOrder/Create.php	2025-05-07 14:59:30
+++ b/Model/AdminOrder/Create.php	2025-05-07 14:42:59
@@ -1342,6 +1342,10 @@
         if ($optionIds) {
             foreach (explode(',', $optionIds->getValue() ?? '') as $optionId) {
                 $option = $item->getProduct()->getOptionById($optionId);
+                if ($option === null) {
+                    continue;
+                }
+
                 $optionValue = $item->getOptionByCode('option_' . $optionId)->getValue();

                 $group = $this->_objectManager->get(
