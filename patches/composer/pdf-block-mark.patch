diff --git a/vendor/magento/module-sales/Model/Order/Pdf/AbstractPdf.php b/vendor/magento/module-sales/Model/Order/Pdf/AbstractPdf.php
--- a/vendor/magento/module-sales/Model/Order/Pdf/AbstractPdf.php
+++ b/vendor/magento/module-sales/Model/Order/Pdf/AbstractPdf.php	(date 1743964531239)
@@ -473,7 +473,7 @@
         $billingAddress = $this->_formatAddress($this->addressRenderer->format($order->getBillingAddress(), 'pdf'));

         /* Payment */
-        $paymentInfo = $this->_paymentData->getInfoBlock($order->getPayment())->setIsSecureMode(true)->toPdf();
+        $paymentInfo = $this->_paymentData->getInfoBlock($order->getPayment())->setPdfOverwrite(true)->setIsSecureMode(true)->toPdf();
         $paymentInfo = $paymentInfo !== null ? htmlspecialchars_decode($paymentInfo, ENT_QUOTES) : '';
         $payment = explode('{{pdf_row_separator}}', $paymentInfo);
         foreach ($payment as $key => $value) {
