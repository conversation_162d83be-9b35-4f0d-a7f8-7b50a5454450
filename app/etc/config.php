<?php
return [
    'scopes' => [
        'websites' => [
            'admin' => [
                'website_id' => '0',
                'code' => 'admin',
                'name' => 'Admin',
                'sort_order' => '0',
                'default_group_id' => '0',
                'is_default' => '0'
            ],
            'base' => [
                'website_id' => '1',
                'code' => 'base',
                'name' => 'ComAve',
                'sort_order' => '1',
                'default_group_id' => '1',
                'is_default' => '1'
            ]
        ],
        'groups' => [
            [
                'group_id' => '0',
                'website_id' => '0',
                'name' => 'Default',
                'root_category_id' => '0',
                'default_store_id' => '0',
                'code' => 'default'
            ],
            [
                'group_id' => '1',
                'website_id' => '1',
                'name' => 'ComAve',
                'root_category_id' => '2',
                'default_store_id' => '1',
                'code' => 'main_website_store'
            ]
        ],
        'stores' => [
            'admin' => [
                'store_id' => '0',
                'code' => 'admin',
                'website_id' => '0',
                'group_id' => '0',
                'name' => 'Admin',
                'sort_order' => '0',
                'is_active' => '1'
            ],
            'english' => [
                'store_id' => '1',
                'code' => 'english',
                'website_id' => '1',
                'group_id' => '1',
                'name' => 'English',
                'sort_order' => '1',
                'is_active' => '1'
            ],
            'fr_store' => [
                'store_id' => '2',
                'code' => 'fr_store',
                'website_id' => '1',
                'group_id' => '1',
                'name' => 'France',
                'sort_order' => '2',
                'is_active' => '1'
            ],
            'it_store' => [
                'store_id' => '3',
                'code' => 'it_store',
                'website_id' => '1',
                'group_id' => '1',
                'name' => 'Italy',
                'sort_order' => '3',
                'is_active' => '1'
            ],
            'es_store' => [
                'store_id' => '4',
                'code' => 'es_store',
                'website_id' => '1',
                'group_id' => '1',
                'name' => 'Spain',
                'sort_order' => '4',
                'is_active' => '1'
            ],
            'nl_store' => [
                'store_id' => '5',
                'code' => 'nl_store',
                'website_id' => '1',
                'group_id' => '1',
                'name' => 'Netherlands',
                'sort_order' => '5',
                'is_active' => '1'
            ]
        ]
    ],
    'system' => [
        'stores' => [
            'english' => [
                'general' => [
                    'locale' => [
                        'timezone' => 'Europe/London',
                        'code' => 'en_GB'
                    ]
                ],
                'currency' => [
                    'options' => [
                        'default' => 'GBP'
                    ]
                ]
            ],
            'fr_store' => [
                'general' => [
                    'locale' => [
                        'timezone' => 'Europe/Paris',
                        'code' => 'fr_FR'
                    ]
                ],
                'currency' => [
                    'options' => [
                        'default' => 'EUR'
                    ]
                ]
            ],
            'it_store' => [
                'general' => [
                    'locale' => [
                        'timezone' => 'Europe/Rome',
                        'code' => 'it_IT'
                    ]
                ],
                'currency' => [
                    'options' => [
                        'default' => 'EUR'
                    ]
                ]
            ],
            'es_store' => [
                'general' => [
                    'locale' => [
                        'timezone' => 'Europe/Madrid',
                        'code' => 'es_ES'
                    ]
                ],
                'currency' => [
                    'options' => [
                        'default' => 'EUR'
                    ]
                ]
            ],
            'nl_store' => [
                'general' => [
                    'locale' => [
                        'timezone' => 'Europe/Amsterdam',
                        'code' => 'nl_NL'
                    ]
                ],
                'currency' => [
                    'options' => [
                        'default' => 'EUR'
                    ]
                ]
            ]
        ],
        'default' => [
            'admin' => [
                'security' => [
                    'admin_account_sharing' => 0
                ]
            ],
            'web' => [
                'session' => [
                    'use_remote_addr' => '1',
                    'use_http_user_agent' => '1'
                ]
            ],
            'comave_frontend' => [
                'disable_frontend' => [
                    'allowed_frontName_list' => 'directory/currency/switch/*,loginascustomer/*,stripe/*,admin_ni2d8miur,swat,baseshipping,mpbundle,mpgroupedproduct,mpmultishopifystoremageconnect,mprmasystem,uploade,mpsellercategory,mpsellercoupons,mpsellergroup,mpsellermaplocator,mpsellerproductsearch,mpsplitorder,vendorattribute,vendorgroup,requestforquote,swagger,coditron_customshippingrate,seller_payouts,stores,mooauth,marketplace/,marketplace/account/*,marketplace/mui_index/render/*,marketplace/index/*,shipping/tracking/popup,marketplace/product/*,customer/account/loginPost,customer/account/forgotpassword,customer/account/createpost,customer/account/confirm,customer/section/load,customer/account/createPassword,customer/account/createpassword,customer/account/resetpasswordpost,customer/account,customerfiles/invoice/download,stores,shook,returnaddress,robots.txt,mui/*'
                ]
            ],
            'comave_webapi_restrictions' => [
                'general' => [
                    'restrict_order_place' => 1
                ]
            ],
            'category_matcher' => [
                'general' => [
                    'endpoint' => 'https://categories-matcher-s.comave.com/catmap/batch'
                ]
            ],
            'general' => [
                'locale' => [
                    'code' => 'en_US'
                ],
                'countries' => [
                    'eu_countries' => 'AT,BE,BG,CY,CZ,DK,EE,FI,FR,DE,GR,HR,HU,IE,IT,LV,LT,LU,MT,NL,PL,PT,RO,SK,SI,ES,SE,GB'
                ]
            ],
            'marketplace' => [
                'product_settings' => [
                    'attributesetid' => '4',
                    'allow_for_seller' => 'simple,configurable',
                    'disable_seller_create_attribute' => 1
                ]
            ],
            'currency' => [
                'options' => [
                    'allow' => 'AUD,GBP,BGN,CZK,EUR,HUF,PLN,QAR,RON,TRY,USD,AED,SEK'
                ]
            ],
            'dev' => [
                'static' => [
                    'sign' => '1'
                ],
                'front_end_development_workflow' => [
                    'type' => 'server_side_compilation'
                ],
                'template' => [
                    'minify_html' => '0',
                    'allow_symlink' => null
                ],
                'js' => [
                    'merge_files' => '0',
                    'enable_js_bundling' => '0',
                    'minify_files' => '0',
                    'move_script_to_bottom' => '0',
                    'translate_strategy' => 'dictionary',
                    'session_storage_logging' => '0',
                    'minify_exclude' => [
                        'tiny_mce' => '/tiny_mce/',
                        'cardinal_commerce' => '/v1/songbird'
                    ]
                ],
                'css' => [
                    'merge_css_files' => null,
                    'minify_files' => '0',
                    'use_css_critical_path' => '0',
                    'minify_exclude' => [
                        'tiny_mce' => '/tiny_mce/'
                    ]
                ]
            ],
            'advanced' => [
                'modules_disable_output' => [
                    'Magento_Banner' => '1'
                ]
            ]
        ]
    ],
    'modules' => [
        'Magento_Store' => 1,
        'Magento_Config' => 1,
        'Magento_AdminAnalytics' => 1,
        'Magento_Directory' => 1,
        'Magento_AdminNotification' => 1,
        'Magento_AdminGwsConfigurableProduct' => 1,
        'Magento_AdminGwsStaging' => 1,
        'Magento_Theme' => 1,
        'Magento_Eav' => 1,
        'Magento_AdobeCommerceOutOfProcessExtensibility' => 1,
        'Magento_AdobeIms' => 1,
        'Magento_AdobeIoEventsClient' => 1,
        'Magento_AdobeCommerceWebhooks' => 1,
        'Magento_AdobeCommerceWebhooksGenerator' => 1,
        'Magento_AdobeCommerceEventsClient' => 1,
        'Magento_AdobeImsApi' => 1,
        'Magento_AdobeCommerceEventsGenerator' => 1,
        'Magento_AdobeStockAdminUi' => 1,
        'Magento_MediaGallery' => 1,
        'Magento_AdobeStockAssetApi' => 1,
        'Magento_AdobeStockClient' => 1,
        'Magento_AdobeStockClientApi' => 1,
        'Magento_AdobeStockImage' => 1,
        'Magento_Variable' => 1,
        'Magento_AdobeStockImageApi' => 1,
        'Magento_Customer' => 1,
        'Magento_Indexer' => 1,
        'Magento_AdvancedPricingImportExport' => 1,
        'Magento_Rule' => 1,
        'Magento_Cms' => 1,
        'Magento_Backend' => 1,
        'Magento_Amqp' => 1,
        'Magento_Security' => 1,
        'Magento_ApplicationPerformanceMonitor' => 1,
        'Magento_ApplicationPerformanceMonitorNewRelic' => 1,
        'Magento_Cookie' => 1,
        'Magento_ApplicationServerNewRelic' => 1,
        'Magento_ApplicationServerPerformanceMonitor' => 1,
        'Magento_ApplicationServerStateMonitor' => 1,
        'Magento_ApplicationServerStateMonitorGraphQl' => 1,
        'Magento_AsyncConfig' => 1,
        'Magento_Catalog' => 1,
        'Magento_Authorization' => 1,
        'Magento_User' => 1,
        'Magento_GraphQl' => 1,
        'Magento_MediaStorage' => 1,
        'Magento_AwsS3CustomerCustomAttributes' => 1,
        'Magento_GiftCardImportExport' => 1,
        'Magento_Widget' => 1,
        'Magento_ImportExport' => 1,
        'Magento_AdminAdobeIms' => 1,
        'Magento_Backup' => 1,
        'Magento_CatalogRule' => 1,
        'Magento_Payment' => 1,
        'Magento_Quote' => 1,
        'Magento_SalesSequence' => 1,
        'Magento_Sales' => 1,
        'Magento_SalesRule' => 1,
        'Magento_Bundle' => 1,
        'Magento_EavGraphQl' => 1,
        'Magento_BundleImportExport' => 1,
        'Magento_BundleImportExportStaging' => 1,
        'Magento_DataExporter' => 1,
        'Magento_Search' => 1,
        'Magento_CatalogInventory' => 1,
        'Magento_CacheInvalidate' => 1,
        'Magento_Checkout' => 1,
        'Magento_CardinalCommerce' => 1,
        'Magento_AdminGws' => 1,
        'Magento_Integration' => 1,
        'Magento_GraphQlResolverCache' => 1,
        'Magento_StoreGraphQl' => 1,
        'Magento_CatalogDataExporter' => 1,
        'Magento_CatalogSearch' => 1,
        'Magento_AsyncOrder' => 1,
        'Magento_CatalogImportExport' => 1,
        'Magento_CatalogImportExportStaging' => 1,
        'Magento_CommerceBackendUix' => 1,
        'Magento_CatalogInventoryDataExporter' => 1,
        'Magento_CatalogInventoryGraphQl' => 1,
        'Magento_CatalogUrlRewrite' => 1,
        'Magento_CatalogPageBuilderAnalytics' => 1,
        'Magento_CatalogPageBuilderAnalyticsStaging' => 1,
        'Magento_Ui' => 1,
        'Magento_CatalogGraphQl' => 1,
        'Magento_CustomerCustomAttributes' => 1,
        'Magento_Msrp' => 1,
        'Magento_CatalogRuleGraphQl' => 1,
        'Magento_VersionsCms' => 1,
        'Magento_Captcha' => 1,
        'Magento_Downloadable' => 1,
        'Magento_Staging' => 1,
        'Magento_GiftCard' => 1,
        'Magento_GraphQlServer' => 0,
        'Magento_AdminGraphQlServer' => 0,
        'Magento_Wishlist' => 1,
        'Magento_UrlRewrite' => 1,
        'Magento_UrlRewriteGraphQl' => 1,
        'Magento_ConfigurableProduct' => 1,
        'Magento_Robots' => 1,
        'Magento_QuoteGraphQl' => 1,
        'Magento_CheckoutAddressSearch' => 1,
        'Magento_GiftRegistry' => 1,
        'Magento_CheckoutAgreements' => 1,
        'Magento_CheckoutAgreementsGraphQl' => 1,
        'Magento_CheckoutStaging' => 0,
        'Magento_PageCache' => 1,
        'Magento_CmsGraphQl' => 1,
        'Magento_CmsPageBuilderAnalytics' => 1,
        'Magento_CmsPageBuilderAnalyticsStaging' => 1,
        'Magento_CmsStaging' => 1,
        'Magento_CmsUrlRewrite' => 1,
        'Magento_CmsUrlRewriteGraphQl' => 1,
        'Magento_AdminUiSdkCustomFees' => 1,
        'Magento_CompareListGraphQl' => 1,
        'Magento_TwoFactorAuth' => 0,
        'Magento_ConfigurableImportExport' => 1,
        'Magento_CatalogRuleConfigurable' => 1,
        'Magento_ConfigurableProductDataExporter' => 0,
        'Magento_ConfigurableProductGraphQl' => 1,
        'Magento_ConfigurableProductSales' => 1,
        'Magento_ProductAlert' => 1,
        'Magento_Contact' => 1,
        'Magento_ContactGraphQl' => 1,
        'Magento_ApplicationServer' => 1,
        'Magento_Cron' => 1,
        'Magento_Csp' => 1,
        'Magento_CurrencySymbol' => 1,
        'Magento_CustomAttributeManagement' => 1,
        'Magento_AdvancedCatalog' => 1,
        'Magento_Analytics' => 1,
        'Magento_CustomerBalance' => 1,
        'Magento_CustomerBalanceGraphQl' => 1,
        'Magento_CustomerSegment' => 1,
        'Magento_CustomerCustomAttributesGraphQl' => 1,
        'Magento_DownloadableGraphQl' => 1,
        'Magento_CustomerFinance' => 1,
        'Magento_Newsletter' => 1,
        'Magento_CustomerImportExport' => 1,
        'Magento_CatalogWidget' => 1,
        'Magento_CustomerSegmentGraphQl' => 1,
        'Magento_BundleProductDataExporter' => 0,
        'Magento_ServicesConnector' => 1,
        'Magento_Multishipping' => 1,
        'Magento_DeferredTotalCalculating' => 1,
        'Magento_Deploy' => 1,
        'Magento_Developer' => 1,
        'Magento_Dhl' => 1,
        'Magento_BundleGraphQl' => 1,
        'Magento_DirectoryGraphQl' => 1,
        'Magento_TargetRule' => 1,
        'Magento_CustomerDownloadableGraphQl' => 1,
        'Magento_DownloadableImportExport' => 1,
        'Magento_Tax' => 1,
        'Magento_AdvancedCheckout' => 1,
        'Magento_CatalogCustomerGraphQl' => 1,
        'Magento_AdvancedSearch' => 1,
        'Magento_Elasticsearch' => 1,
        'Magento_WebsiteRestriction' => 1,
        'Magento_ElasticsearchCatalogPermissionsGraphQl' => 1,
        'Magento_Email' => 1,
        'Magento_EncryptionKey' => 1,
        'Magento_Enterprise' => 1,
        'Magento_Fedex' => 1,
        'Magento_CatalogEvent' => 1,
        'Magento_GiftCardAccount' => 1,
        'Magento_GiftCardAccountGraphQl' => 1,
        'Magento_CustomerGraphQl' => 1,
        'Magento_Sitemap' => 1,
        'Magento_GiftCardProductDataExporter' => 0,
        'Magento_VisualMerchandiser' => 1,
        'Magento_GiftMessage' => 1,
        'Magento_GiftMessageGraphQl' => 1,
        'Magento_GiftMessageStaging' => 1,
        'Magento_CatalogPermissions' => 1,
        'Magento_GiftRegistryGraphQl' => 1,
        'Magento_GiftWrapping' => 1,
        'Magento_GiftWrappingGraphQl' => 1,
        'Magento_GiftWrappingStaging' => 1,
        'Magento_GoogleAdwords' => 1,
        'Magento_GoogleAnalytics' => 1,
        'Magento_GoogleGtag' => 1,
        'Magento_GoogleOptimizer' => 1,
        'Magento_GoogleOptimizerStaging' => 1,
        'Magento_Banner' => 1,
        'Magento_ServicesId' => 1,
        'Magento_GraphQlCache' => 1,
        'Magento_GraphQlNewRelic' => 1,
        'Magento_CatalogCmsGraphQl' => 1,
        'Magento_SaaSCommon' => 0,
        'Magento_GroupedProduct' => 1,
        'Magento_GroupedImportExport' => 1,
        'Magento_GroupedCatalogInventory' => 1,
        'Magento_GroupedProductGraphQl' => 1,
        'Magento_Weee' => 1,
        'Magento_ImportCsv' => 1,
        'Magento_ImportCsvApi' => 1,
        'Magento_RemoteStorage' => 1,
        'Magento_ImportJson' => 1,
        'Magento_ImportJsonApi' => 1,
        'Magento_AdvancedRule' => 1,
        'Magento_InstantPurchase' => 1,
        'Magento_CatalogAnalytics' => 1,
        'Magento_IntegrationGraphQl' => 1,
        'Magento_Inventory' => 1,
        'Magento_InventoryAdminUi' => 1,
        'Magento_InventoryAdvancedCheckout' => 1,
        'Magento_InventoryApi' => 1,
        'Magento_InventoryBundleImportExport' => 1,
        'Magento_InventoryBundleProduct' => 1,
        'Magento_InventoryBundleProductAdminUi' => 1,
        'Magento_InventoryBundleProductIndexer' => 1,
        'Magento_InventoryCatalog' => 1,
        'Magento_InventorySales' => 1,
        'Magento_InventoryCatalogAdminUi' => 1,
        'Magento_InventoryCatalogApi' => 1,
        'Magento_InventoryCatalogFrontendUi' => 1,
        'Magento_InventoryCatalogSearch' => 1,
        'Magento_InventoryCatalogSearchBundleProduct' => 1,
        'Magento_InventoryCatalogSearchConfigurableProduct' => 1,
        'Magento_InventoryConfigurableProduct' => 1,
        'Magento_InventoryConfigurableProductAdminUi' => 1,
        'Magento_InventoryConfigurableProductFrontendUi' => 1,
        'Magento_InventoryConfigurableProductIndexer' => 1,
        'Magento_InventoryConfiguration' => 1,
        'Magento_InventoryConfigurationApi' => 1,
        'Magento_InventoryDistanceBasedSourceSelection' => 1,
        'Magento_InventoryDistanceBasedSourceSelectionAdminUi' => 1,
        'Magento_InventoryDistanceBasedSourceSelectionApi' => 1,
        'Magento_InventoryElasticsearch' => 1,
        'Magento_InventoryExportStockApi' => 1,
        'Magento_InventoryIndexer' => 1,
        'Magento_InventorySalesApi' => 1,
        'Magento_InventoryGroupedProduct' => 1,
        'Magento_InventoryGroupedProductAdminUi' => 1,
        'Magento_InventoryGroupedProductIndexer' => 1,
        'Magento_InventoryImportExport' => 1,
        'Magento_InventoryCache' => 1,
        'Magento_InventoryLowQuantityNotification' => 1,
        'Magento_Reports' => 1,
        'Magento_InventoryLowQuantityNotificationApi' => 1,
        'Magento_InventoryMultiDimensionalIndexerApi' => 1,
        'Magento_InventoryProductAlert' => 1,
        'Magento_InventoryQuoteGraphQl' => 1,
        'Magento_InventoryRequisitionList' => 1,
        'Magento_InventoryReservations' => 1,
        'Magento_InventoryReservationCli' => 1,
        'Magento_InventoryReservationsApi' => 1,
        'Magento_InventoryExportStock' => 1,
        'Magento_InventorySalesAdminUi' => 1,
        'Magento_InventoryGraphQl' => 1,
        'Magento_InventorySalesAsyncOrder' => 1,
        'Magento_InventorySalesFrontendUi' => 1,
        'Magento_InventorySetupFixtureGenerator' => 0,
        'Magento_InventoryShipping' => 1,
        'Magento_Shipping' => 1,
        'Magento_InventorySourceDeductionApi' => 1,
        'Magento_InventorySourceSelection' => 1,
        'Magento_InventorySourceSelectionApi' => 1,
        'Magento_InventorySwatchesFrontendUi' => 1,
        'Magento_InventoryVisualMerchandiser' => 1,
        'Magento_InventoryWishlist' => 1,
        'Magento_Invitation' => 1,
        'Magento_JwtFrameworkAdapter' => 1,
        'Magento_JwtUserToken' => 1,
        'Magento_LayeredNavigation' => 1,
        'Magento_LayeredNavigationStaging' => 1,
        'Magento_Logging' => 1,
        'Magento_LoginAsCustomer' => 1,
        'Magento_LoginAsCustomerAdminUi' => 1,
        'Magento_LoginAsCustomerApi' => 1,
        'Magento_LoginAsCustomerAssistance' => 1,
        'Magento_LoginAsCustomerFrontendUi' => 1,
        'Magento_LoginAsCustomerGraphQl' => 1,
        'Magento_LoginAsCustomerLog' => 1,
        'Magento_LoginAsCustomerLogging' => 1,
        'Magento_LoginAsCustomerPageCache' => 1,
        'Magento_LoginAsCustomerQuote' => 1,
        'Magento_LoginAsCustomerSales' => 1,
        'Magento_LoginAsCustomerWebsiteRestriction' => 1,
        'Magento_MediaContent' => 1,
        'Magento_MediaContentApi' => 1,
        'Magento_MediaContentCatalog' => 1,
        'Magento_MediaContentCatalogStaging' => 1,
        'Magento_MediaContentCms' => 1,
        'Magento_MediaContentSynchronization' => 1,
        'Magento_MediaContentSynchronizationApi' => 1,
        'Magento_MediaContentSynchronizationCatalog' => 1,
        'Magento_MediaContentSynchronizationCms' => 1,
        'Magento_AdobeStockAsset' => 1,
        'Magento_MediaGalleryApi' => 1,
        'Magento_MediaGalleryCatalog' => 1,
        'Magento_MediaGalleryCatalogIntegration' => 1,
        'Magento_MediaGalleryCatalogUi' => 1,
        'Magento_MediaGalleryCmsUi' => 1,
        'Magento_MediaGalleryIntegration' => 1,
        'Magento_MediaGalleryMetadata' => 1,
        'Magento_MediaGalleryMetadataApi' => 1,
        'Magento_MediaGalleryRenditions' => 1,
        'Magento_MediaGalleryRenditionsApi' => 1,
        'Magento_MediaGallerySynchronization' => 1,
        'Magento_MediaGallerySynchronizationApi' => 1,
        'Magento_MediaGallerySynchronizationMetadata' => 1,
        'Magento_MediaGalleryUi' => 1,
        'Magento_MediaGalleryUiApi' => 1,
        'Magento_AwsS3' => 1,
        'Magento_MessageQueue' => 1,
        'Magento_CatalogStaging' => 1,
        'Magento_MsrpConfigurableProduct' => 1,
        'Magento_MsrpGroupedProduct' => 1,
        'Magento_MsrpStaging' => 1,
        'Magento_Multicoupon' => 1,
        'Magento_MulticouponGraphQl' => 1,
        'Magento_AdvancedSalesRule' => 1,
        'Magento_MultipleWishlist' => 1,
        'Magento_SalesGraphQl' => 1,
        'Magento_DataServices' => 0,
        'Magento_MysqlMq' => 1,
        'Magento_NewRelicReporting' => 1,
        'Magento_WishlistGraphQl' => 1,
        'Magento_NewsletterGraphQl' => 1,
        'Magento_OfflinePayments' => 1,
        'Magento_OfflineShipping' => 1,
        'Magento_OpenSearch' => 1,
        'Magento_OrderCancellation' => 1,
        'Magento_OrderCancellationGraphQl' => 1,
        'Magento_OrderCancellationUi' => 1,
        'Magento_BannerCustomerSegment' => 1,
        'Magento_PageBuilder' => 1,
        'Magento_PageBuilderAdminGwsAdminUi' => 1,
        'Magento_PageBuilderAnalytics' => 1,
        'Magento_ProductRecommendationsAdmin' => 0,
        'Magento_ConfigurableProductStaging' => 1,
        'Magento_ParentProductDataExporter' => 0,
        'Magento_BannerGraphQl' => 1,
        'Magento_PaymentGraphQl' => 1,
        'Magento_ServiceProxy' => 1,
        'Magento_Vault' => 1,
        'Magento_PaymentServicesDashboard' => 1,
        'Magento_PaymentServicesPaypalGraphQl' => 1,
        'Magento_QueryXml' => 1,
        'Magento_PaymentStaging' => 1,
        'Magento_ServicesIdGraphQlServer' => 0,
        'Magento_Paypal' => 0,
        'Magento_PaypalGraphQl' => 0,
        'Magento_Persistent' => 1,
        'Magento_PersistentHistory' => 1,
        'Magento_PricePermissions' => 1,
        'Magento_CatalogStagingPageBuilder' => 1,
        'Magento_ProductOverrideDataExporter' => 0,
        'Magento_ProductPriceDataExporter' => 0,
        'Magento_ProductRecommendationsLayout' => 0,
        'Magento_PageBuilderProductRecommendations' => 0,
        'Magento_ProductVideo' => 1,
        'Magento_ProductVideoStaging' => 1,
        'Magento_PromotionPermissions' => 1,
        'Magento_ServicesIdLayout' => 0,
        'Magento_BannerPageBuilderAnalytics' => 1,
        'Magento_QuoteAnalytics' => 1,
        'Magento_QuoteBundleOptions' => 1,
        'Magento_QuoteCommerceGraphQl' => 1,
        'Magento_QuoteConfigurableOptions' => 1,
        'Magento_QuoteDownloadableLinks' => 1,
        'Magento_QuoteGiftCardOptions' => 1,
        'Magento_AsyncOrderGraphQl' => 1,
        'Magento_QuoteStaging' => 1,
        'Magento_ReCaptchaAdminUi' => 1,
        'Magento_ReCaptchaCheckout' => 1,
        'Magento_ReCaptchaCheckoutSalesRule' => 1,
        'Magento_ReCaptchaContact' => 1,
        'Magento_ReCaptchaCustomer' => 1,
        'Magento_ReCaptchaFrontendUi' => 1,
        'Magento_ReCaptchaGiftCard' => 1,
        'Magento_ReCaptchaInvitation' => 1,
        'Magento_ReCaptchaMigration' => 1,
        'Magento_ReCaptchaMultipleWishlist' => 1,
        'Magento_ReCaptchaNewsletter' => 1,
        'Magento_ReCaptchaPaypal' => 1,
        'Magento_ReCaptchaReview' => 1,
        'Magento_ReCaptchaSendFriend' => 1,
        'Magento_ReCaptchaStorePickup' => 1,
        'Magento_ReCaptchaUi' => 1,
        'Magento_ReCaptchaUser' => 1,
        'Magento_ReCaptchaValidation' => 1,
        'Magento_ReCaptchaValidationApi' => 1,
        'Magento_ReCaptchaVersion2Checkbox' => 1,
        'Magento_ReCaptchaVersion2Invisible' => 1,
        'Magento_ReCaptchaVersion3Invisible' => 1,
        'Magento_ReCaptchaWebapiApi' => 1,
        'Magento_ReCaptchaWebapiGraphQl' => 1,
        'Magento_ReCaptchaWebapiRest' => 1,
        'Magento_ReCaptchaWebapiUi' => 1,
        'Magento_ReCaptchaWishlist' => 1,
        'Magento_RelatedProductGraphQl' => 1,
        'Magento_ReleaseNotification' => 1,
        'Magento_Reminder' => 1,
        'Magento_AwsS3GiftCardImportExport' => 1,
        'Magento_RemoteStorageCommerce' => 1,
        'Magento_InventoryLowQuantityNotificationAdminUi' => 1,
        'Magento_RequireJs' => 1,
        'Magento_ResourceConnections' => 1,
        'Magento_Review' => 1,
        'Magento_ReviewAnalytics' => 1,
        'Magento_ReviewGraphQl' => 1,
        'Magento_ReviewStaging' => 1,
        'Magento_Reward' => 1,
        'Magento_RewardGraphQl' => 1,
        'Magento_SalesRuleStaging' => 1,
        'Magento_Rma' => 1,
        'Magento_RmaGraphQl' => 1,
        'Magento_RmaStaging' => 1,
        'Magento_ScheduledImportExport' => 1,
        'Magento_Rss' => 1,
        'Magento_MulticouponUi' => 1,
        'Magento_SaaSCatalog' => 0,
        'Magento_SaaSCategory' => 1,
        'Magento_CatalogSyncAdminGraphQlServer' => 1,
        'Magento_SaaSPrice' => 0,
        'Magento_SaaSProductOverride' => 0,
        'Magento_ScopesDataExporter' => 0,
        'Magento_BannerPageBuilder' => 1,
        'Magento_SalesAnalytics' => 1,
        'Magento_SalesArchive' => 1,
        'Magento_PaymentServicesBase' => 1,
        'Magento_MultipleWishlistGraphQl' => 1,
        'Magento_SalesInventory' => 1,
        'Magento_BannerStaging' => 1,
        'Magento_SalesRuleGraphQl' => 1,
        'Magento_RewardStaging' => 1,
        'Magento_CatalogRuleStaging' => 1,
        'Magento_SampleData' => 1,
        'Magento_ScalableCheckout' => 1,
        'Magento_ScalableInventory' => 1,
        'Magento_ScalableOms' => 1,
        'Magento_AwsS3ScheduledImportExport' => 1,
        'Magento_SaaSScopes' => 0,
        'Magento_Elasticsearch7' => 1,
        'Magento_SearchStaging' => 1,
        'Magento_CustomerAnalytics' => 1,
        'Magento_Securitytxt' => 1,
        'Magento_SendFriend' => 1,
        'Magento_SendFriendGraphQl' => 1,
        'Magento_Webapi' => 1,
        'Magento_DataServicesMultishipping' => 0,
        'Magento_CatalogSyncAdmin' => 0,
        'Magento_SalesDataExporter' => 1,
        'Magento_StoreDataExporter' => 1,
        'Magento_InventoryShippingAdminUi' => 1,
        'Magento_AwsS3PageBuilder' => 1,
        'Magento_StagingGraphQl' => 1,
        'Magento_CatalogStagingGraphQl' => 1,
        'Magento_StagingPageBuilder' => 1,
        'Magento_BundleProductOverrideDataExporter' => 0,
        'Magento_PaymentServicesSaaSExport' => 0,
        'Magento_CatalogPermissionsGraphQl' => 1,
        'Magento_Support' => 1,
        'Magento_Swagger' => 1,
        'Magento_SwaggerWebapi' => 1,
        'Magento_SwaggerWebapiAsync' => 1,
        'Magento_Swat' => 1,
        'Magento_Swatches' => 1,
        'Magento_SwatchesGraphQl' => 1,
        'Magento_SwatchesLayeredNavigation' => 1,
        'Magento_DownloadableStaging' => 1,
        'Magento_TargetRuleGraphQl' => 1,
        'Magento_BundleStaging' => 1,
        'Magento_TaxGraphQl' => 1,
        'Magento_TaxImportExport' => 1,
        'Magento_AdobeStockImageAdminUi' => 1,
        'Magento_ThemeGraphQl' => 1,
        'Magento_Translation' => 1,
        'Magento_AdminAdobeImsTwoFactorAuth' => 1,
        'Magento_ElasticsearchCatalogPermissions' => 1,
        'Magento_Ups' => 1,
        'Magento_CatalogUrlRewriteDataExporter' => 0,
        'Magento_CatalogUrlRewriteGraphQl' => 1,
        'Magento_AsynchronousOperations' => 1,
        'Magento_Usps' => 1,
        'Magento_GiftCardStaging' => 1,
        'Magento_PaypalCaptcha' => 1,
        'Magento_VaultGraphQl' => 1,
        'Magento_Version' => 1,
        'Magento_GoogleTagManager' => 1,
        'Magento_VersionsCmsPageCache' => 1,
        'Magento_VersionsCmsUrlRewrite' => 1,
        'Magento_VersionsCmsUrlRewriteGraphQl' => 1,
        'Magento_CatalogUrlRewriteStaging' => 1,
        'Magento_VisualProductRecommendations' => 0,
        'Magento_PaymentServicesPaypal' => 1,
        'Magento_WebapiAsync' => 1,
        'Magento_WebapiSecurity' => 1,
        'Magento_GroupedProductStaging' => 1,
        'Magento_CatalogInventoryStaging' => 1,
        'Magento_WeeeGraphQl' => 1,
        'Magento_WeeeStaging' => 1,
        'Magento_PageBuilderAdminAnalytics' => 1,
        'Magento_CheckoutAddressSearchGiftRegistry' => 1,
        'Magento_WishlistAnalytics' => 1,
        'Magento_WishlistGiftCard' => 1,
        'Magento_WishlistGiftCardGraphQl' => 1,
        'Magento_GiftCardGraphQl' => 1,
        'Bss_StoreviewFlags' => 1,
        'Coditron_CartRule' => 0,
        'Coditron_Cspvalidate' => 1,
        'Comave_CustomerAddressType' => 1,
        'Webkul_MpSellerGroup' => 1,
        'Comave_ApiConnector' => 1,
        'Comave_AttributeCleanup' => 1,
        'Comave_Logger' => 1,
        'GhoSter_ChangeCustomerPassword' => 1,
        'Comave_Breadcrumbs' => 0,
        'WeltPixel_Backend' => 1,
        'Comave_CategoryCommission' => 1,
        'Webkul_Marketplace' => 1,
        'WeltPixel_EnhancedEmail' => 1,
        'Comave_EmailConfig' => 1,
        'Comave_ComaveApi' => 1,
        'Comave_SellerApi' => 1,
        'Comave_LixApi' => 1,
        'Webkul_MpMultiShopifyStoreMageConnect' => 1,
        'Comave_Marketplace' => 1,
        'Comave_CustomerGroup' => 1,
        'Webkul_MpSellerBuyerCommunication' => 1,
        'Webkul_MpApi' => 1,
        'Comave_DbClean' => 1,
        'Comave_Deploy' => 0,
        'Comave_DisableFrontend' => 1,
        'Comave_DisableZeroPrice' => 1,
        'WeltPixel_ProductLabels' => 1,
        'Comave_EnhancedReviewGraphql' => 1,
        'Comave_ExperienceGraphQl' => 1,
        'Comave_GraphQLMiddleware' => 1,
        'Comave_Integration' => 1,
        'Comave_Club' => 1,
        'Webkul_MpAdvancedCommission' => 1,
        'Comave_LixApiConnector' => 1,
        'Comave_LixOffers' => 1,
        'Comave_Locale' => 1,
        'Comave_SellerOnboarding' => 1,
        'Comave_LoginSession' => 0,
        'Comave_CategoryMatcher' => 1,
        'Comave_MarketplaceApi' => 1,
        'Comave_MaskedEmail' => 1,
        'Comave_Customer' => 1,
        'Comave_MediaCleaner' => 1,
        'Comave_NewsArticles' => 1,
        'Comave_OpenSearch' => 1,
        'Webkul_OutOfStockNotification' => 1,
        'Comave_ProductAutoCategory' => 1,
        'Webkul_MpBundleProduct' => 1,
        'Comave_QuoteGraphQl' => 1,
        'Comave_RaffleQl' => 0,
        'Comave_ReasonManagement' => 1,
        'Comave_ReasonManagementGraphQl' => 1,
        'Comave_ReasonManagementSales' => 1,
        'Comave_ReasonMarketplace' => 1,
        'Comave_Referfriend' => 1,
        'Comave_ReturnAddress' => 1,
        'Comave_Rma' => 1,
        'Comave_Sales' => 1,
        'Comave_RmaMarketplace' => 1,
        'Umc_Crud' => 1,
        'Comave_SecurityHeaders' => 1,
        'Comave_ShopifyAccounts' => 1,
        'Comave_BigBuy' => 1,
        'Comave_SellerPayouts' => 1,
        'Webkul_MpSellerCategory' => 1,
        'Comave_SellerReport' => 1,
        'Comave_SellerRequest' => 0,
        'Comave_SellerShippingCountry' => 1,
        'Comave_ShopifyAccountModifier' => 1,
        'Comave_CatalogGraphQl' => 1,
        'Comave_ShopifyOrderSync' => 1,
        'Webkul_MpMultiShopifyStoreMageConnectExtend' => 1,
        'Comave_Soccer' => 1,
        'Comave_SoccerGraphQl' => 1,
        'Coditron_CustomShippingRate' => 1,
        'Comave_Sportsclub' => 0,
        'Comave_SportsclubFan' => 1,
        'Comave_StoreConfig' => 1,
        'StripeIntegration_Tax' => 1,
        'Comave_StripeTax' => 1,
        'Webkul_MpEasyPost' => 0,
        'Comave_TravellerInfo' => 1,
        'Comave_TravellerInfoGraphQl' => 1,
        'Comave_WebapiRestriction' => 1,
        'Customer_RemoveAccount' => 0,
        'Elgentos_RegenerateCatalogUrls' => 1,
        'EthanYehuda_CronjobManager' => 1,
        'Comave_CrystalPalace' => 1,
        'Graycore_Cors' => 1,
        'IntegerNet_EnableSwagger' => 1,
        'JomaShop_NewRelicMonitoring' => 1,
        'Lof_All' => 1,
        'Lof_CustomerAvatar' => 1,
        'Lof_ProductReviews' => 1,
        'Lof_ProductReviewsGraphQl' => 1,
        'Magetop_DeliveryTime' => 1,
        'Navigate_Core' => 1,
        'Navigate_AllowSvgWebpAvifImage' => 1,
        'PL_Mpgs' => 0,
        'PayPal_Braintree' => 1,
        'PayPal_BraintreeCustomerBalance' => 0,
        'PayPal_BraintreeGiftCardAccount' => 0,
        'PayPal_BraintreeGiftWrapping' => 0,
        'PayPal_BraintreeGraphQl' => 1,
        'StripeIntegration_Payments' => 1,
        'Comave_StripeGraphQl' => 1,
        'Comave_BigBuyShipping' => 1,
        'Webkul_Customoption' => 0,
        'Webkul_FirebaseOTPLogin' => 1,
        'Coditron_Mpmultistorewoocommerce' => 0,
        'Webkul_MarketplaceBaseShipping' => 1,
        'Webkul_MarketplacePreorder' => 1,
        'Comave_RmaGraphQl' => 1,
        'Comave_CustomerLogin' => 1,
        'Comave_ProductOffers' => 1,
        'Comave_TrackingStatus' => 0,
        'Webkul_MpGroupedProduct' => 1,
        'Comave_MapOrderStatuses' => 1,
        'Comave_ShopifyWebhookInstaller' => 1,
        'Comave_CustomerIoEmail' => 1,
        'Comave_SellerProductShippingCost' => 1,
        'Webkul_MpSellerCoupons' => 1,
        'Comave_SplitOrder' => 1,
        'Webkul_MpVendorAttributeManager' => 1,
        'Webkul_Mppercountryperproductshipping' => 1,
        'Comave_OutOfStockNotification' => 1,
        'Webkul_PriceDropAlert' => 1,
        'WeltPixel_AdvanceCategorySorting' => 1,
        'WeltPixel_AdvancedWishlist' => 1,
        'Comave_ClubGraphQl' => 1,
        'WeltPixel_CmsBlockScheduler' => 1,
        'WeltPixel_Command' => 1,
        'WeltPixel_CustomFooter' => 1,
        'WeltPixel_CustomHeader' => 1,
        'WeltPixel_FrontendOptions' => 0,
        'Comave_CustomerGraphQl' => 1,
        'WeltPixel_DesignElements' => 1,
        'WeltPixel_FullPageScroll' => 1,
        'WeltPixel_GoogleCards' => 1,
        'WeltPixel_LayeredNavigation' => 0,
        'WeltPixel_LazyLoading' => 1,
        'WeltPixel_MobileDetect' => 1,
        'WeltPixel_Multistore' => 1,
        'Comave_LixGraphQl' => 1,
        'WeltPixel_ProductPage' => 0,
        'WeltPixel_QuickCart' => 1,
        'WeltPixel_ReviewsWidget' => 1,
        'WeltPixel_SampleData' => 1,
        'WeltPixel_SearchAutoComplete' => 0,
        'WeltPixel_Sitemap' => 1,
        'WeltPixel_SmartProductTabs' => 1,
        'WeltPixel_SpeedOptimization' => 0,
        'WeltPixel_ThankYouPage' => 1,
        'WeltPixel_TitleRewrite' => 1,
        'WeltPixel_UserProfile' => 1
    ],
    'admin_user' => [
        'locale' => [
            'code' => [
                'en_US'
            ]
        ]
    ]
];
