<?php
declare(strict_types=1);

namespace Webkul\Marketplace\Controller\Account;

use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Customer\Api\AccountManagementInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Model\Session as CustomerSession;
use Magento\Customer\Model\AccountManagement;
use Magento\Framework\Exception\NoSuchEntityException;

class ResetPassword extends Action
{
    public function __construct(
        Context $context,
        private AccountManagementInterface $accountManagement,
        private CustomerRepositoryInterface $customerRepository,
        private CustomerSession $customerSession
    ) {
        parent::__construct($context);
    }

    public function execute()
    {
        try {
            $customerId = (int) $this->customerSession->getId();
            $email = $this->customerRepository->getById($customerId)->getEmail();

            $this->accountManagement->initiatePasswordReset(
                $email,
                AccountManagement::EMAIL_RESET
            );

            $this->messageManager->addSuccessMessage(
                __('We sent a password-reset link to %1.', $email)
            );
        } catch (NoSuchEntityException $e) {
            $this->messageManager->addErrorMessage(__('Unable to find your customer account.'));
        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage(__('We could not send the reset e-mail.'));
        }

        return $this->resultRedirectFactory->create()
            ->setPath('marketplace/account/editprofile');
    }
}
