<?php
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Magento\Framework\View\Element\Template $block */
$rmaViewModel = $block->getData('rmaViewModel');
if (!$rmaViewModel instanceof \Comave\RmaMarketplace\ViewModel\Rma) {
    return '';
}

$currentRma = $rmaViewModel->getCurrentRma();
$order = $currentRma->getOrder();
$sellerName = sprintf(
    '%s %s',
    $currentRma->getExtensionAttributes()->getSeller()->getFirstname(),
    $currentRma->getExtensionAttributes()->getSeller()->getLastname(),
);
$customer = $order->getCustomer();
$totalPrice = 0;
$closedStatus = in_array(
    $currentRma->getStatus(),
    [
        \Magento\Rma\Model\Rma\Source\Status::STATE_CLOSED,
        \Magento\Rma\Model\Rma\Source\Status::STATE_PROCESSED_CLOSED
    ]
);
?>
<div class="wk-mprma-container wk-mprma-view">
    <a href="<?= $escaper->escapeUrl($block->getUrl('mprmasystem/seller/allrma')); ?>"
       class="wk-date-info"><?= $escaper->escapeHtml(__("Back")); ?></a>
    <a class="wk-date-info" aria-hidden="true"
       title="<?= $escaper->escapeHtml(__('Print Return Request')) ?>"
       href="<?= $escaper->escapeUrl($block->getUrl(
           'mprmasystem/customer/printpdf/',
           [
               'rma_id' => $currentRma->getId(),
               '_secure' => $block->getIsSecure()
           ]
       )) ?>"
       login-url="<?= $escaper->escapeUrl($block->getUrl('customer/account/login')); ?>">
        <?= $escaper->escapeHtml(__('Print Return Request')) ?>
    </a>
    <div class="wk-title">
        <?= $escaper->escapeHtml(__("Return Request Details")); ?>
        <span class="wk-date-info"><?= $escaper->escapeHtml($currentRma->getDateRequested()); ?></span>
    </div>
    <form method="post"
          action="<?= $escaper->escapeUrl($block->getUrl('mprmasystem/customer/create')); ?>"
          id="wk_new_rma_form" enctype="multipart/form-data" data-mage-init='{"validation":{}}'>
        <fieldset class="fieldset wk-mprma-fieldset">
            <div class="field">
                <label for="Ordrer Id" class="label">
                    <span><?= $escaper->escapeHtml(__("Order Id")); ?></span>
                </label>
                <div class="control">
                    <a target="_blank"
                       href="<?= $escaper->escapeUrl($block->getUrl('sales/order/view', ['order_id' => $currentRma->getOrderId()])); ?>">
                        <?= $escaper->escapeHtml($currentRma->getOrderIncrementId()); ?>
                    </a>
                </div>
            </div>
            <div class="field">
                <label for="Status" class="label">
                    <span><?= $escaper->escapeHtml(__("Status")); ?></span>
                </label>
                <div class="control">
                    <div class="wk-row">
                        <span><?= $escaper->escapeHtml(__("Return Status")); ?> :</span>
                        <?= $escaper->escapeHtml($currentRma->getStatusLabel()); ?>
                    </div>
                </div>
            </div>
        </fieldset>
    </form>
</div>
<div class="wk-mprma-container wk-mprma-view">
    <div class="wk-title wk-close-title">
        <?= $escaper->escapeHtml(__("Change Return Request Status")); ?>
        <?php if (!$closedStatus): ?>
            <div class="wk-action-btn">
                <button title="Submit change"
                        class="action save primary"
                        type="button"
                        onclick="document.getElementById('comave_rma_save_form').submit()">
                    <span><?= $escaper->escapeHtml(__("Save")); ?></span>
                </button>
            </div>
        <?php endif; ?>
    </div>
    <fieldset class="fieldset wk-mprma-fieldset wk-close-filedset">
        <div class="field">
            <?php if (!$closedStatus): ?>
                <form method="post"
                      action="<?= $escaper->escapeUrl($block->getUrl('mprmasystem/rma/change')); ?>"
                      id="comave_rma_save_form" enctype="multipart/form-data"
                      data-mage-init='{"validation":{}}'>
                    <input type="hidden" value="<?= $escaper->escapeHtml($currentRma->getEntityId()); ?>" name="rma_id">
                    <?= $block->getChildBlock('items_grid')->toHtml(); ?>
                </form>
            <?php else: ?>
                <?= $escaper->escapeHtml(__('RMA is Closed')); ?>
            <?php endif; ?>
        </div>
    </fieldset>
</div>
<div class="wk-mprma-container wk-mprma-view">
    <div class="wk-title">
        <?= $escaper->escapeHtml(__("Item(s) Requested for Return")); ?>
    </div>
    <div class="wk-table-wrapper">
        <table id="mprma-items-table" class="data table">
            <thead>
            <tr>
                <th class="col" scope="col"><?= $escaper->escapeHtml(__("Product Name")); ?></th>
                <th class="col" scope="col"><?= $escaper->escapeHtml(__("Sku")); ?></th>
                <th class="col" scope="col"><?= $escaper->escapeHtml(__("Price")); ?></th>
                <th class="col" scope="col"><?= $escaper->escapeHtml(__("Qty")); ?></th>
                <th class="col" scope="col"><?= $escaper->escapeHtml(__("Reason")); ?></th>
            </tr>
            </thead>
            <?php /** @var \Magento\Rma\Model\Item $rmaItem */ ?>
            <?php foreach ($currentRma->getItems() as $rmaItem): ?>
                  <?php
                     $totalPrice += $rmaViewModel->getItemPrice($rmaItem->getProductSku(), $order, true)
                ?>
                <tbody>
                <tr>
                    <td class="col">
                        <?= $escaper->escapeHtml($rmaItem->getProductName()); ?>
                    </td>
                    <td class="col"><?= $escaper->escapeHtml($rmaItem->getProductSku()); ?></td>
                    <td class="col">
                        <?= /* @noEscape */ $rmaViewModel->getItemPrice($rmaItem->getProductSku(), $order); ?>
                    </td>
                    <td class="col"><?= $escaper->escapeHtml($rmaItem->getQtyRequested()); ?></td>
                    <td class="col"><?= $escaper->escapeHtml($currentRma->getCustomAttribute('reason')?->getValue() ?: '-'); ?>
                    </td>
                </tr>
                </tbody>
            <?php endforeach; ?>
        </table>
    </div>
</div>
<?php if (!empty($currentRma->getComments())): ?>
    <div class="wk-mprma-container wk-mprma-view">
        <div class="wk-title">
            <?= $escaper->escapeHtml(__("Conversations")); ?>
        </div>
        <?php foreach ($currentRma->getComments() as $conversation): ?>
            <?php
            $date = $conversation->getCreatedAt();
            $date = $block->formatDate($date, \IntlDateFormatter::MEDIUM, true);
            $type = (int) $conversation->getIsAdmin();
            $text = match ($type) {
                2 => $text = __("Seller") . " : " . $sellerName,
                0 => $text = __("Customer") . " : " . $order->getCustomerName() ?: $currentRma->getCustomerId(),
                default => $text = __("Administrator"),
            };
            $class = match ($type) {
                2 => $class = "wk-mprma-seller-section",
                0 => $class = "wk-mprma-customer-section",
                default => $class = "wk-mprma-admin-section"
            };
            ?>
            <div class="wk-mprma-conversation">
                <div class="wk-mprma-conversation-head <?= $escaper->escapeHtml($class); ?>">
                    <div class="wk-mprma-conversation-head-left">
                        <?= $escaper->escapeHtml($date); ?>
                    </div>
                    <div class="wk-mprma-conversation-head-right">
                        <?= $escaper->escapeHtml($text); ?>
                    </div>
                </div>
                <div class="wk-mprma-conversation-body">
                    <?= $escaper->escapeHtml($conversation->getComment()); ?>
                </div>
            </div>
        <?php endforeach; ?>
        <?php if ($block->getPagerHtml()): ?>
            <div class="order-products-toolbar toolbar bottom"><?= /* @noEscape */
                $block->getPagerHtml(); ?></div>
        <?php endif ?>
    </div>
<?php endif ?>
<div class="wk-mprma-container wk-mprma-view">
    <div class="wk-title">
        <?= $escaper->escapeHtml(__("Send Message")); ?>
    </div>
    <form method="post"
          action="<?= $escaper->escapeUrl($block->getUrl('mprmasystem/rma/conversation')); ?>"
          id="wk_rma_conversation_form" data-mage-init='{"validation":{}}'>
        <input name="form_key" type="hidden" value="<?= $escaper->escapeHtmlAttr($block->getFormKey()) ?>" />
        <input type="hidden" value="<?= $escaper->escapeHtml(2); ?>" name="sender_type">
        <input type="hidden" value="<?= $escaper->escapeHtml($currentRma->getEntityId()); ?>" name="rma_id">
        <fieldset class="fieldset wk-mprma-fieldset wk-close-filedset">
            <div class="field required">
                <label for="Enter Message" class="label">
                    <span><?= $escaper->escapeHtml(__("Enter Message")); ?></span>
                </label>
                <div class="control">
                    <textarea name="message"
                              data-validate="{required:true}"
                              class="wk-rma-msg input-text required-entry
                        validate-no-html-tags"></textarea>
                </div>
                <div class="wk-action-btn">
                    <button title="Save" class="action save primary wk-send" type="submit">
                        <span><?= $escaper->escapeHtml(__("Send Message")); ?></span>
                    </button>
                </div>
            </div>
        </fieldset>
    </form>
</div>
<?php
$data = json_encode([
    "totalPrice" => $totalPrice,
    "totalPriceWithCurrency" => $order->formatPrice($totalPrice),
    "errorMsg" => __("Partial amount can not be more then %1", $order->formatPrice($totalPrice)),
    "warningLable" => __("Warning")
]);
?>
<script type="text/x-magento-init">
    {
        "body": {
            "Comave_RmaMarketplace/js/rma": <?= /* @noEscape */ $data ?>
    }
}
</script>
