<?php

declare(strict_types=1);

namespace Comave\RmaMarketplace\ViewModel;

use Comave\RmaMarketplace\Api\SellerLinkManagementInterface;
use Magento\Customer\Model\Session;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Registry;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Rma\Api\Data\RmaInterface;
use Magento\Rma\Api\RmaRepositoryInterface;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Model\Order\Item;

class Rma implements ArgumentInterface
{
    public const string CURRENT_RMA = 'current_rma';

    /**
     * @param Registry $registry
     * @param RequestInterface $request
     * @param Session $sellerSession
     * @param RmaRepositoryInterface $rmaRepository
     * @param SellerLinkManagementInterface $sellerLinkManagement
     */
    public function __construct(
        private readonly Registry $registry,
        private readonly RequestInterface $request,
        private readonly Session $sellerSession,
        private readonly RmaRepositoryInterface $rmaRepository,
        private readonly SellerLinkManagementInterface $sellerLinkManagement
    ) {
    }

    /**
     * @return RmaInterface
     * @throws LocalizedException
     */
    public function getCurrentRma(): RmaInterface
    {
        $currentRma = $this->registry->registry(self::CURRENT_RMA);

        if (empty($currentRma)) {
            $currentRma = $this->rmaRepository->get($this->request->getParam('id'));
            $this->registry->register(self::CURRENT_RMA, $currentRma);
        }

        if (!$currentRma->getExtensionAttributes()->getSeller()) {
            throw new LocalizedException(__('Unable to identify seller for current rma'));
        }

        if ($currentRma->getExtensionAttributes()->getSeller()->getId() !== $this->sellerSession->getCustomerId()) {
            throw new LocalizedException(__('Current rma does not belong to the seller'));
        }

        return $currentRma;
    }

    /**
     * @param string $rmaProductSku
     * @param OrderInterface $order
     * @param bool $asFloat
     * @return string
     */
    public function getItemPrice(string $rmaProductSku, OrderInterface $order, bool $asFloat = false): string
    {
        $orderItems = $order->getAllVisibleItems();

        /** @var Item $orderItem */
        foreach ($orderItems as $orderItem) {
            if ($orderItem->getSku() !== $rmaProductSku) {
                continue;
            }

            return $asFloat ? (string) $orderItem->getPrice() : $order->formatPrice(
                $orderItem->getPrice()
            );
        }

        return '0.00';
    }
}
