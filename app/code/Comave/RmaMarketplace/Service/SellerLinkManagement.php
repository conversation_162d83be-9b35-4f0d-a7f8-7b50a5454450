<?php

declare(strict_types=1);

namespace Comave\RmaMarketplace\Service;

use Comave\Rma\Service\SellerReturnAddressProvider;
use Comave\RmaMarketplace\Api\SellerLinkManagementInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Exception\LocalizedException;
use Magento\Rma\Api\Data\RmaInterface;
use Magento\Rma\Api\RmaRepositoryInterface;

class SellerLinkManagement implements SellerLinkManagementInterface
{
    /**
     * @param SellerReturnAddressProvider $sellerReturnAddressProvider
     * @param ResourceConnection $resourceConnection
     * @param CustomerRepositoryInterface $customerRepository
     * @param RmaRepositoryInterface $rmaRepository
     */
    public function __construct(
        private readonly SellerReturnAddressProvider $sellerReturnAddressProvider,
        private readonly ResourceConnection $resourceConnection,
        private readonly CustomerRepositoryInterface $customerRepository,
        private readonly RmaRepositoryInterface $rmaRepository,
    ) {
    }

    /**
     * @param RmaInterface $rma
     * @return RmaInterface
     * @throws LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function assign(RmaInterface $rma): RmaInterface
    {
        $connection = $this->resourceConnection->getConnection('write');
        $seller = $rma->getExtensionAttributes()?->getSeller() ?? null;

        if (!empty($seller)) {
            $connection->insertOnDuplicate(
                self::TABLE_NAME,
                [
                    self::RMA_ID => $rma->getEntityId(),
                    self::SELLER_ID => $seller->getId()
                ]
            );

            return $rma;
        }

        $sellerAddresses = $this->sellerReturnAddressProvider->get($rma->getOrder());

        if (empty($sellerAddresses)) {
            throw new LocalizedException(__('Unable to identify seller for given RMA'));
        }

        //@todo split order
        $address = current($sellerAddresses);
        $connection->insertOnDuplicate(
            self::TABLE_NAME,
            [
                self::RMA_ID => $rma->getEntityId(),
                self::SELLER_ID => $address['parent_id']
            ]
        );

        return $rma;
    }

    /**
     * @param RmaInterface $rma
     * @return CustomerInterface
     * @throws LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function get(RmaInterface $rma): CustomerInterface
    {
        $connection = $this->resourceConnection->getConnection('read');
        $selectFrom = $connection->select()
            ->from(
                self::TABLE_NAME,
                [
                    self::SELLER_ID,
                ]
            )
            ->where(
                self::RMA_ID . ' = ?',
                $rma->getEntityId()
            );

        $sellerId = $connection->fetchCol($selectFrom) ?: false;

        if (empty($sellerId)) {
            throw new LocalizedException(__('Unable to identify seller for given RMA'));
        }

        return $this->customerRepository->getById((int) $sellerId);
    }

    /**
     * @param string $rmaId
     * @return CustomerInterface
     * @throws LocalizedException
     */
    public function getById(string $rmaId): CustomerInterface
    {
        $rma = $this->rmaRepository->get((int) $rmaId);

        return $this->get($rma);
    }
}
