<?php

declare(strict_types=1);

namespace Comave\RmaMarketplace\Controller\Seller;

use Magento\Customer\Controller\AccountInterface;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\Registry;
use Magento\Rma\Api\RmaRepositoryInterface;
use Magento\Sales\Api\OrderRepositoryInterface;
use Webkul\Marketplace\Helper\Data;

class Rma extends \Magento\Framework\App\Action\Action implements HttpGetActionInterface, AccountInterface
{
    /**
     * @param Context $context
     * @param Data $mpHelper
     * @param OrderRepositoryInterface $orderRepository
     * @param RmaRepositoryInterface $rmaRepository
     * @param Registry $registry
     */
    public function __construct(
        Context $context,
        private readonly Data $mpHelper,
        private readonly OrderRepositoryInterface $orderRepository,
        private readonly RmaRepositoryInterface $rmaRepository,
        private readonly Registry $registry,
    ) {
        parent::__construct($context);
    }

    /**
     * Rma Action
     *
     * @return \Magento\Framework\View\Result\Page
     */
    public function execute()
    {
        if (!$this->mpHelper->isSeller()) {
            return $this->resultFactory->create(ResultFactory::TYPE_FORWARD)->forward('noroute');
        } else {
            try {
                $rma = $this->rmaRepository->get($this->getRequest()->getParam('id'));
                $order = $this->orderRepository->get($rma->getOrderId());
                $this->registry->register('current_order', $order);
                $this->registry->register('current_rma', $rma);
            } catch (\Exception) {
                $this->messageManager->addErrorMessage(__('Unable to load RMA Entity'));

                return $this->resultFactory->create(ResultFactory::TYPE_FORWARD)->forward('noroute');
            }

            $resultPage = $this->resultFactory->create(ResultFactory::TYPE_PAGE);
            $resultPage->addHandle('mprmasystem_seller_rma_layout2');
            $resultPage->getConfig()->getTitle()->set(__('View Return Request'));

            return $resultPage;
        }
    }
}
