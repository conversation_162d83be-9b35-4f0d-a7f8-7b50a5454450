<?php

declare(strict_types=1);

namespace Comave\RmaMarketplace\Plugin;

use Magento\Framework\Model\AbstractModel;
use Magento\Rma\Model\ResourceModel\Rma\Status\History;
use Webkul\Marketplace\Helper\Data;

class SetSellerType
{
    /**
     * @param Data $mpHelper
     */
    public function __construct(private readonly Data $mpHelper)
    {
    }

    /**
     * @param History $historyResource
     * @param AbstractModel $rmaHistoryComment
     * @return AbstractModel[]|null
     */
    public function beforeSave(
        History $historyResource,
        AbstractModel $rmaHistoryComment
    ): ?array {
        if (!$this->mpHelper->isSeller()) {
            return null;
        }

        $rmaHistoryComment->setIsAdmin(2);

        return [$rmaHistoryComment];
    }
}
