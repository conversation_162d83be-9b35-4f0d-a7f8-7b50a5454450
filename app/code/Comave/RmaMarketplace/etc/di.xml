<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Comave\RmaMarketplace\Api\SellerLinkManagementInterface" type="Comave\RmaMarketplace\Service\SellerLinkManagement"/>

    <type name="Magento\Rma\Model\ResourceModel\Rma">
        <plugin name="addSellerLink" type="Comave\RmaMarketplace\Plugin\AddSellerLink"/>
    </type>

    <type name="Magento\Rma\Model\ResourceModel\Rma\Status\History">
        <plugin name="setSellerHistoryFlag" type="Comave\RmaMarketplace\Plugin\SetSellerType"/>
    </type>
</config>
