<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="comave_marketplace_rma_to_seller" resource="default" engine="innodb" comment="Marketplace RMA Seller Mapping Table">
        <column xsi:type="int" name="id" padding="11" unsigned="false" nullable="false" identity="true" comment="Id"/>
        <column xsi:type="int" name="seller_id" nullable="false" unsigned="true" identity="false" comment="Seller ID"/>
        <column xsi:type="int" name="rma_id" unsigned="true" nullable="false" identity="false" comment="RMA ID"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="id"/>
        </constraint>
        <constraint xsi:type="foreign"
                    referenceId="SELLER_ID_TO_MP_RMA_SELLER_ID"
                    table="marketplace_rma_to_seller"
                    column="seller_id"
                    referenceTable="customer_entity"
                    referenceColumn="entity_id"
                    onDelete="NO ACTION"/>
        <constraint xsi:type="foreign"
                    referenceId="RMA_ID_TO_MP_RMA_ID"
                    table="marketplace_rma_to_seller"
                    column="rma_id"
                    referenceTable="magento_rma"
                    referenceColumn="entity_id"
                    onDelete="NO ACTION"/>
        <constraint xsi:type="unique" referenceId="COMAVE_MP_SELLER_RMA_UNIQ">
            <column name="seller_id"/>
            <column name="rma_id"/>
        </constraint>
    </table>
</schema>
