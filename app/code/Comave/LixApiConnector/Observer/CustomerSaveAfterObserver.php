<?php
declare(strict_types=1);

namespace Comave\LixApiConnector\Observer;

use AllowDynamicProperties;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;
use Magento\Framework\App\Request\Http;
use Magento\Customer\Model\Customer;
use Magento\Customer\Model\CustomerFactory;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Reward\Model\ResourceModel\Reward\HistoryFactory;
use Magento\Reward\Model\RewardFactory;

#[AllowDynamicProperties]
class CustomerSaveAfterObserver implements ObserverInterface
{
    private bool $isFlagSet = false;

    public function __construct(
        \Comave\LixApiConnector\Helper\Data $dataHelper,
        \Magento\Framework\Message\ManagerInterface $messageManager,
        CustomerRepositoryInterface $customerRepository,
        ScopeConfigInterface $scopeConfig,
        Customer $Customer,
        CustomerFactory $customerFactory,
        RewardFactory $rewardFactory,
        HistoryFactory $rewardHistoryFactory,
        Http $request,
        \Magento\Store\Model\StoreManagerInterface $storeManager
    ) {
        $this->dataHelper = $dataHelper;
        $this->_messageManager = $messageManager;
        $this->customerRepository = $customerRepository;
        $this->scopeConfig = $scopeConfig;
        $this->Customer = $Customer;
        $this->customerFactory = $customerFactory;
        $this->rewardFactory = $rewardFactory;
        $this->rewardHistoryFactory = $rewardHistoryFactory;
        $this->request = $request;
        $this->storeManager = $storeManager;
    }

    public function execute(\Magento\Framework\Event\Observer $observer)
    {
        if ($this->isFlagSet) {
            return;
        }

        $this->isFlagSet = true;
        $customer = $observer->getEvent()->getCustomer();
        $customerId = $customer->getId();

        $profileData = 0;
        if (array_key_exists('customerclub', $_REQUEST)) {
            $clubFanId = $_REQUEST['customerclub'];
        } else {
            $clubFanId = '';
        }

        $customerDetails = $this->Customer->load($customerId);
        $customerData = $customerDetails->getDataModel();
        $custName = $customerDetails->getFirstname();
        $customerEmail = $customerDetails->getEmail();

        $customeratt = $this->customerRepository->getById($customerId);
        $customerPhoneNumber = $this->request->getParam('phone_no', '');
        if (!empty($customerPhoneNumber)) {
            $customeratt->setCustomAttribute('phone_no', $customerPhoneNumber);
        }
    }

    public function saveBalanceToRewardTable($rewardBalance, $customerId)
    {
        $store = $this->storeManager->getStore();
        $websiteId = $store->getWebsiteId();

        $customer = $this->customerRepository->getById($customerId);
        $reward = $this->rewardFactory->create()->setCustomer($customer)
            ->setCustomerId($customerId)
            ->setWebsiteId($websiteId)
            ->setPointsDelta($rewardBalance)
            ->setAction(\Magento\Reward\Model\Reward::REWARD_ACTION_ADMIN)
            ->updateRewardPoints();
        $reward->save();
    }
}
