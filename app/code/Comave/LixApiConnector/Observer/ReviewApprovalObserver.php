<?php
namespace Comave\LixApiConnector\Observer;

use AllowDynamicProperties;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Event\Observer;
use Magento\Customer\Model\Session as CustomerSession;
use Magento\Sales\Model\OrderFactory;
use Magento\Review\Model\ReviewFactory;
use Magento\Customer\Model\CustomerFactory;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Reward\Model\ResourceModel\Reward\HistoryFactory;
use Magento\Reward\Model\RewardFactory;
use Psr\Log\LoggerInterface;

#[AllowDynamicProperties]
class ReviewApprovalObserver implements ObserverInterface
{
    protected $customerSession;
    protected $orderFactory;
    protected $reviewFactory;
    protected $rewardFactory;

    public function __construct(
        CustomerSession $customerSession,
        OrderFactory $orderFactory,
        \Magento\Framework\Message\ManagerInterface $messageManager,
        CustomerRepositoryInterface $customerRepository,
        ReviewFactory $reviewFactory,
        CustomerFactory $customerFactory,
        RewardFactory $rewardFactory,
        HistoryFactory $rewardHistoryFactory,
        \Comave\LixApiConnector\Helper\Data $dataHelper,
        \Magento\Framework\Mail\Template\TransportBuilder $transportBuilder,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Magento\Framework\Translate\Inline\StateInterface $inlineTranslation,
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        LoggerInterface $logger
    ) {
        $this->customerSession = $customerSession;
        $this->orderFactory = $orderFactory;
        $this->_messageManager = $messageManager;
        $this->reviewFactory = $reviewFactory;
        $this->customerFactory = $customerFactory;
        $this->customerRepository = $customerRepository;
        $this->rewardFactory = $rewardFactory;
        $this->rewardHistoryFactory = $rewardHistoryFactory;
        $this->dataHelper = $dataHelper;
        $this->transportBuilder = $transportBuilder;
        $this->storeManager = $storeManager;
        $this->inlineTranslation = $inlineTranslation;
        $this->scopeConfig = $scopeConfig;
        $this->logger = $logger;
    }

    public function execute(Observer $observer)
    {

        $review = $observer->getEvent()->getData('object');
        $status = $review->getStatusId();
        if ($status == \Magento\Review\Model\Review::STATUS_APPROVED) {
            $customerId = $review->getCustomerId();
            $productId = $review->getEntityPkValue();
            // Check if customer has ordered the product
            $orders = $this->orderFactory->create()->getCollection()
                ->addFieldToFilter('customer_id', $customerId);

            $orderedProductIds = [];
            foreach ($orders as $order) {
                foreach ($order->getAllVisibleItems() as $item) {
                    $orderedProductIds[] = $item->getProductId();
                }
            }

            if (in_array($productId, $orderedProductIds)) {
                // Customer has ordered the product, proceed with review approval

                $customer = $this->customerRepository->getById($customerId);

                $customerEmail = $customer->getEmail();
                $customerName = $customer->getFirstname();
                $customerData = $this->getCustomer($customerEmail);
                $customerPhone = $customerData->getPhoneNo();
                $customformCollection = [];

                foreach ($customformCollection as $customformModel) {
                    $taskTitle = $customformModel->getTaskTitle();
                    $lixwalet = 'LIXCA';
                    if($lixwalet){
                        if($lixwalet == 'LIXCA'){
                            if($taskTitle == 'Review On Purchased Product - LIXCA'){
                                $taskId = $customformModel->getTaskId();

                                    $fanTask = $taskId;
                                    $activityData = array();
                                    $activityData['phone'] = $customerPhone;
                                    $activityData['email'] = $customerEmail;
                                    $activityData['name'] = $customerName;
                                    $postActivityData = json_encode($activityData);

                                    $lixActivityData = $this->dataHelper->getLixActivity($fanTask,$postActivityData);

                                    $messsage = $lixActivityData['message'];
                                    $rewardBalance = $lixActivityData['data']['coins_earned'];
                                    // $this->_messageManager->addSuccessMessage(__($messsage));
                                    $comment = "You've earned LIX rewards on product review.";

                                    //Set reward on table
                                    $this->saveBalanceToRewardTable($rewardBalance, $customerId, $comment);
                                     // Send email to customer
                                     $templateOptions = array('area' => \Magento\Framework\App\Area::AREA_FRONTEND, 'store' => $this->storeManager->getStore()->getId());
                                     $templateVars = array(
                                                 'customer_name' => $customerName,
                                                 'reward_balance' => $rewardBalance,
                                                 'message'    => 'Hello World!!.');

                                     $from = array('email' => "<EMAIL>", 'name' => 'Your Review Has Been Approved');
                                     $this->inlineTranslation->suspend();
                                     $transport = $this->transportBuilder->setTemplateIdentifier('lix_review_approval')
                                                     ->setTemplateOptions($templateOptions)
                                                     ->setTemplateVars($templateVars)
                                                     ->setFrom($from)
                                                     ->addTo($customerEmail)
                                                     ->getTransport();

                                     $transport->sendMessage();
                                     $this->inlineTranslation->resume();
                                    // $this->sendReviewApprovalEmail($customerEmail, $customerName, $customerPhone, $rewardBalance);
                            }
                        }elseif($lixwalet == 'LIXX'){
                            if($taskTitle == 'Review On Purchased Product - LIXX'){
                                    $taskId = $customformModel->getTaskId();

                                    $fanTask = $taskId;
                                    $activityData = array();
                                    $activityData['phone'] = $customerPhone;
                                    $activityData['email'] = $customerEmail;
                                    $activityData['name'] = $customerName;
                                    $postActivityData = json_encode($activityData);

                                    $lixActivityData = $this->dataHelper->getLixActivity($fanTask,$postActivityData);

                                    // $messsage = $lixActivityData['message'];
                                    $rewardBalance = $lixActivityData['data']['coins_earned'];
                                    // $this->_messageManager->addSuccessMessage(__($messsage));
                                    $comment = "You've earned LIX rewards on product review.";

                                    //Set reward on table
                                    $this->saveBalanceToRewardTable($rewardBalance, $customerId, $comment);
                                     // Send email to customer
                                    $templateOptions = array('area' => \Magento\Framework\App\Area::AREA_FRONTEND, 'store' => $this->storeManager->getStore()->getId());
                                    $templateVars = array(
                                                 'customer_name' => $customerName,
                                                 'reward_balance' => $rewardBalance,
                                                 'message'    => 'Hello World!!.');

                                    $from = array('email' => "<EMAIL>", 'name' => 'Your Review Has Been Approved');
                                    $this->inlineTranslation->suspend();
                                    $transport = $this->transportBuilder->setTemplateIdentifier('lix_review_approval')
                                                     ->setTemplateOptions($templateOptions)
                                                     ->setTemplateVars($templateVars)
                                                     ->setFrom($from)
                                                     ->addTo($customerEmail)
                                                     ->getTransport();

                                    $transport->sendMessage();
                                    $this->inlineTranslation->resume();
                                    // $this->sendReviewApprovalEmail($customerEmail, $customerName, $customerPhone, $rewardBalance);
                            }
                        }
                    }
                }

            } else {
                // Customer has not ordered the product, do not approve the review
                $this->logger->info('Customer has not ordered the product. Review not approved.');
            }
        }
    }

    public function saveBalanceToRewardTable($rewardBalance,$customerId,$comment)
    {

        $store = $this->storeManager->getStore();
        $websiteId = $store->getWebsiteId();

        $orders = $this->orderFactory->create()->getCollection()
                    ->addFieldToFilter('customer_id', $customerId);

        $customer = $this->customerRepository->getById($customerId);
        $customerEmail = $customer->getEmail();
        $customerName = $customer->getFirstname();


        $reward = $this->rewardFactory->create()->setCustomer($customer)
                                        ->setCustomerId($customerId)
                                        ->setWebsiteId($websiteId)
                                        ->setPointsDelta($rewardBalance)
                                        ->setComment($comment)
                                        ->setAction(\Magento\Reward\Model\Reward::REWARD_ACTION_ADMIN)
                                        ->updateRewardPoints();
        $reward->save();

    }


    public function getCustomer($email)
    {
        try {
            $websiteId = $this->storeManager->getStore()->getWebsiteId();
            $customer = $this->customerFactory->create();
            $customer->setWebsiteId($websiteId);
            $customer->loadByEmail($email);
            return $customer;
        } catch (\Exception $e) {
            $this->logger->error('Something went wrong with customer' . $e->getMessage());
        }
    }
}
