<?php
namespace Comave\LixApiConnector\Controller\Index;

use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Customer\Model\Session;
use Magento\Framework\Controller\ResultFactory;

class Get extends Action
{
    protected $resultJsonFactory;
    protected $customerSession;
    protected $collectionFactory;
    protected $customerRepositoryInterface;
    protected $apiHelper;

    public function __construct(
        Context $context,
        JsonFactory $resultJsonFactory,
        Session $customerSession,
        \Magento\Customer\Api\CustomerRepositoryInterface $customerRepositoryInterface,
        \Comave\LixApiConnector\Helper\Data $apiHelper
    ) {
        $this->resultJsonFactory = $resultJsonFactory;
        $this->customerSession = $customerSession;
        $this->customerRepositoryInterface = $customerRepositoryInterface;
        $this->apiHelper = $apiHelper;
        parent::__construct($context);
    }

    public function execute()
    {
        $result = $this->resultFactory->create(ResultFactory::TYPE_JSON);

        return $result->setData(['success' => true, 'data' => []]);
//        // Get customer ID from session
//        if ($this->customerSession->isLoggedIn()) {
//            $customerId = $this->customerSession->getCustomerId();
//            $logger->info(print_r($customerId, true).' Customer Id');
//        } else {
//            $result = $this->resultJsonFactory->create();
//            return $result->setData(['error' => true, 'message' => 'User not logged in']);
//        }
//
//        $customeratt = $this->customerRepositoryInterface->getById($customerId);
//        $lixuid = null;
//        if ($customeratt->getCustomAttribute('lix_uid')) {
//            $lixuid = $customeratt->getCustomAttribute('lix_uid')->getValue();
//            $logger->info(print_r($lixuid, true).' lix Uid');
//        }
//        // $logger->info(print_r($customeratt, true).' response');
//
//        $collection = $this->collectionFactory->create();
//        $notificationLogs = $collection->getItems();
//        $results = [];
//        foreach ($notificationLogs as $log) {
//            $data = $log->getData();
//            if (isset($data['body'])) {
//                $bodyData = json_decode($data['body'], true);
//                $results[] = $bodyData;
//            }
//        }
//        $logger->info(print_r($results, true).' Result');
//
//        $matchingResults = [];
//        foreach ($results as $result) {
//            if (isset($result['user_id']) && $result['user_id'] == $lixuid) {
//                $matchingResults[] = $result;
//            }
//        }
//        $logger->info(print_r($matchingResults, true).' Matching Results');
//
//        // Sort matchingResults by match_date in ascending order
//        usort($matchingResults, function($a, $b) {
//            return strtotime($a['match_date']) - strtotime($b['match_date']);
//        });
//        $logger->info(print_r($matchingResults, true).' Sorted Matching Results');
//
//        // Filter out results older than two days from the current date
//        $twoDaysAgo = strtotime('-2 days');
//        $filteredResults = array_filter($matchingResults, function($result) use ($twoDaysAgo) {
//            return strtotime($result['match_date']) > $twoDaysAgo;
//        });
//
//        $logger->info(print_r($filteredResults, true).' Filter Response');
//        $result = $this->resultJsonFactory->create();
//        return $result->setData(['success' => true, 'data' => $filteredResults]);
    }
}
