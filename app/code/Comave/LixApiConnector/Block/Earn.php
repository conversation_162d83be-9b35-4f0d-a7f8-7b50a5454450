<?php
namespace Comave\LixApiConnector\Block;

use Magento\Framework\UrlInterface;
use Magento\Framework\App\Request\Http;
use Magento\Reward\Model\RewardFactory;


class Earn extends \Magento\Framework\View\Element\Template
{
    /**
    *  @var \Magento\Customer\Api\CustomerRepositoryInterface $customerRepositoryInterface
    */
    protected $helperData;
    protected $urlBuilder;
    protected $request;
    protected $_customerRepositoryInterface;

    /**
     * @param \Magento\Framework\View\Element\Template\Context $context
     * @param \Comave\LixApiConnector\Helper\Data $helperData
     * @param UrlInterface $urlBuilder
     * @param Http $request
     * @param \Magento\Customer\Model\Session $customerSession
     * @param \Magento\Customer\Api\CustomerRepositoryInterface $customerRepositoryInterface
     * @param RewardFactory $rewardFactory
     * @param \Magento\Store\Model\StoreManagerInterface $storeManager
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \Comave\LixApiConnector\Helper\Data $helperData,
        UrlInterface $urlBuilder,
        Http $request,
        \Magento\Customer\Model\Session $customerSession,
        \Magento\Customer\Api\CustomerRepositoryInterface $customerRepositoryInterface,
        RewardFactory $rewardFactory,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        array $data = []
    ) {
        $this->helperData = $helperData;
        $this->urlBuilder = $urlBuilder;
        $this->request = $request;
        $this->_customerSession = $customerSession;
        $this->_customerRepositoryInterface = $customerRepositoryInterface;
        $this->_rewardFactory = $rewardFactory;
        $this->_storeManager = $storeManager;
        parent::__construct($context, $data);
    }

    public function getTaskCollection()
    {
        return false;
    }

    public function taskCollection()
    {
        $taskCollection = $this->getTaskCollection();
        return $taskCollection;

    }

    public function storeUrl(){
        return $this->_storeManager->getStore()->getBaseUrl();
    }

    public function customerWallet(){
        $customerId = $this->_customerSession->getCustomer()->getId();
        $customer = $this->_customerRepositoryInterface->getById($customerId);
        $websiteId = $customer->getWebsiteId();
        $email = $customer->getEmail();
        $lixwallet = $customer->getCustomAttribute('lix_wallet');
        if ($lixwallet) {
            $value = $lixwallet->getValue();
            return $value;
        }
    }
}
