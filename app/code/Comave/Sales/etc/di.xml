<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Magento\Sales\Block\Order\History" type="Comave\Sales\Block\Order\History"/>
    <preference for="Comave\Sales\Api\PostProcessingMessageInterface" type="Comave\Sales\Model\PostProcessingMessage"/>
    <virtualType name="OrderPostProcessingLogger" type="Comave\Logger\Model\ComaveLogger">
        <arguments>
            <argument name="name" xsi:type="string">OrderPostProcessingLogger</argument>
            <argument name="loggerPath" xsi:type="string">order_post_processing</argument>
        </arguments>
    </virtualType>
    <virtualType name="OrderProcessorTypeManager" type="Comave\Sales\Model\ProcessorTypeManager">
        <arguments>
            <argument name="processorTypes" xsi:type="array">
                <item name="reward_type_processor" xsi:type="string">Comave\Sales\Model\Processor\Type\Reward</item>
                <item name="seller_email_type_processor" xsi:type="string">
                    Comave\Sales\Model\Processor\Type\SellerEmail
                </item>
            </argument>
        </arguments>
    </virtualType>
    <type name="Comave\Sales\Model\Queue\Consumer\PlaceOrderPostProcessing">
        <arguments>
            <argument xsi:type="object" name="processorTypeManager">OrderProcessorTypeManager</argument>
            <argument xsi:type="object" name="logger">OrderPostProcessingLogger</argument>
        </arguments>
    </type>
    <type name="Comave\Sales\Model\Processor\Type\Reward">
        <arguments>
            <argument xsi:type="object" name="logger">OrderPostProcessingLogger</argument>
        </arguments>
    </type>
    <type name="Comave\Sales\Model\Processor\Type\SellerEmail">
        <arguments>
            <argument xsi:type="object" name="logger">OrderPostProcessingLogger</argument>
        </arguments>
    </type>
    <virtualType name="RewardTypeFactory" type="Comave\Sales\Model\RewardTypeFactory">
        <arguments>
            <argument name="rewardTypes" xsi:type="array">
                <item name="reward_type_lix" xsi:type="string">Comave\Sales\Model\Reward\Type\Lix</item>
            </argument>
        </arguments>
    </virtualType>
    <type name="Comave\Sales\Service\Reward\Points\Calculator">
        <arguments>
            <argument xsi:type="object" name="rewardTypeFactory">RewardTypeFactory</argument>
        </arguments>
    </type>

    <type name="Magento\Sales\Block\Adminhtml\Order\View\Items">
        <plugin name="appendImage" type="Comave\Sales\Plugin\AppendProductImage"/>
    </type>

    <type name="Magento\Sales\Block\Adminhtml\Order\View\Items\Renderer\DefaultRenderer">
        <plugin name="appendImageRenderer" type="Comave\Sales\Plugin\AppendProductImageRenderer"/>
    </type>

    <type name="Magento\Quote\Model\Quote">
        <plugin name="preventMultipleSellerProducts" type="Comave\Sales\Plugin\PreventMultipleSellerItems"/>
    </type>
    <type name="Magento\Sales\Controller\Adminhtml\Order\AddComment">
        <plugin name="allowDeliveredStatus"
                type="Comave\Sales\Plugin\AllowDeliveredStatus"
                sortOrder="1"/>
    </type>
</config>
