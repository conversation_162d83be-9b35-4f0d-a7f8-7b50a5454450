<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <!-- Plugin to log and rewrite URLs in final email content -->
    <type name="Magento\Email\Model\Template">
        <plugin name="simple_email_url_rewriter" type="Comave\EmailConfig\Plugin\SimpleEmailUrlPlugin" sortOrder="10"/>
    </type>

    <!-- Plugin to log TransportBuilder email processing -->
    <type name="Magento\Framework\Mail\Template\TransportBuilder">
        <plugin name="email_transport_logger" type="Comave\EmailConfig\Plugin\TransportBuilderLoggerPlugin" sortOrder="5"/>
    </type>

    <!-- Plugin to log Template Filter processing -->
    <type name="Magento\Email\Model\Template\Filter">
        <plugin name="email_filter_logger" type="Comave\EmailConfig\Plugin\TemplateFilterLoggerPlugin" sortOrder="5"/>
    </type>

    <!-- Plugin to log URL generation -->
    <type name="Magento\Framework\Url">
        <plugin name="url_generation_logger" type="Comave\EmailConfig\Plugin\UrlLoggerPlugin" sortOrder="5"/>
    </type>

    <!-- Plugin to log Store URL generation -->
    <type name="Magento\Store\Model\Store">
        <plugin name="store_url_logger" type="Comave\EmailConfig\Plugin\StoreUrlLoggerPlugin" sortOrder="5"/>
    </type>
</config>
