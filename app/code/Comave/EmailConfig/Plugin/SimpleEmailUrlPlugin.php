<?php
declare(strict_types=1);

namespace Comave\EmailConfig\Plugin;

use Comave\EmailConfig\Model\Config\Config;
use Magento\Email\Model\Template;
use Psr\Log\LoggerInterface;

/**
 * Simple plugin to rewrite URLs in email content from backend to frontend domain
 */
class SimpleEmailUrlPlugin
{
    public function __construct(
        private readonly Config $config,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Rewrite URLs in final email content
     *
     * @param Template $subject
     * @param string $result
     * @return string
     */
    public function afterProcessTemplate(Template $subject, string $result): string
    {
        $this->logger->info('EMAIL_FLOW_STEP_38: SimpleEmailUrlPlugin::afterProcessTemplate called');
        $this->logger->info('EMAIL_FLOW_STEP_39: Template ID: ' . $subject->getId());
        $this->logger->info('EMAIL_FLOW_STEP_40: Template Type: ' . $subject->getType());
        $this->logger->info('EMAIL_FLOW_STEP_41: Original content length: ' . strlen($result) . ' characters');

        // Log if content contains URLs before rewriting
        if (strpos($result, 'http') !== false) {
            $this->logger->info('EMAIL_FLOW_STEP_42: Original content contains HTTP URLs');

            // Extract and log URLs found
            preg_match_all('/https?:\/\/[^\s<>"\']+/', $result, $matches);
            if (!empty($matches[0])) {
                foreach ($matches[0] as $url) {
                    $this->logger->info('EMAIL_FLOW_STEP_43: Original URL found: ' . $url);
                }
            }
        }

        $this->logger->info('EMAIL_FLOW_STEP_44: Starting URL rewriting process');
        $rewrittenResult = $this->rewriteUrls($result);

        if ($rewrittenResult !== $result) {
            $this->logger->info('EMAIL_FLOW_STEP_45: SUCCESS - URLs were rewritten in email content');
            $this->logger->info('EMAIL_FLOW_STEP_46: Rewritten content length: ' . strlen($rewrittenResult) . ' characters');

            // Log rewritten URLs
            preg_match_all('/https?:\/\/[^\s<>"\']+/', $rewrittenResult, $rewrittenMatches);
            if (!empty($rewrittenMatches[0])) {
                foreach ($rewrittenMatches[0] as $url) {
                    $this->logger->info('EMAIL_FLOW_STEP_47: Rewritten URL: ' . $url);
                }
            }
        } else {
            $this->logger->info('EMAIL_FLOW_STEP_48: No URLs were rewritten (no changes made)');
        }

        $this->logger->info('EMAIL_FLOW_STEP_49: SimpleEmailUrlPlugin processing completed');

        return $rewrittenResult;
    }

    /**
     * Rewrite URLs in email content
     *
     * @param string $content
     * @return string
     */
    private function rewriteUrls(string $content): string
    {
        $this->logger->info('EMAIL_FLOW_STEP_50: Starting domain detection');

        // Use hardcoded domain mapping for now (simpler approach)
        $domainMapping = [
            'mcstaging.comave.com' => 'staging.comave.com',
            'mc.comave.com' => 'comave.com',
            'mcdev.comave.com' => 'dev.comave.com',
            'localhost:8080' => 'localhost:3000',
        ];

        $this->logger->info('EMAIL_FLOW_STEP_51: Using hardcoded domain mapping: ' . json_encode($domainMapping));
        $this->logger->info('EMAIL_FLOW_STEP_52: Email content preview (first 200 chars): ' . substr($content, 0, 200));

        $urlsRewritten = false;

        foreach ($domainMapping as $backendDomain => $frontendDomain) {
            $this->logger->info('EMAIL_FLOW_STEP_53: Processing domain pair: ' . $backendDomain . ' -> ' . $frontendDomain);

            // Replace URLs in href attributes
            $originalContent = $content;
            $content = preg_replace_callback(
                '/href=["\']([^"\']*' . preg_quote($backendDomain, '/') . '[^"\']*)["\']/',
                function ($matches) use ($backendDomain, $frontendDomain) {
                    $url = str_replace($backendDomain, $frontendDomain, $matches[1]);
                    $this->logger->info('EMAIL_FLOW_STEP_54: Rewritten href URL: ' . $matches[1] . ' -> ' . $url);
                    return 'href="' . $url . '"';
                },
                $content
            );

            if ($content !== $originalContent) {
                $this->logger->info('EMAIL_FLOW_STEP_55: href URLs were rewritten for domain: ' . $backendDomain);
                $urlsRewritten = true;
            }

            // Replace URLs in src attributes
            $originalContent = $content;
            $content = preg_replace_callback(
                '/src=["\']([^"\']*' . preg_quote($backendDomain, '/') . '[^"\']*)["\']/',
                function ($matches) use ($backendDomain, $frontendDomain) {
                    $url = str_replace($backendDomain, $frontendDomain, $matches[1]);
                    $this->logger->info('EMAIL_FLOW_STEP_56: Rewritten src URL: ' . $matches[1] . ' -> ' . $url);
                    return 'src="' . $url . '"';
                },
                $content
            );

            if ($content !== $originalContent) {
                $this->logger->info('EMAIL_FLOW_STEP_57: src URLs were rewritten for domain: ' . $backendDomain);
                $urlsRewritten = true;
            }

            // Replace plain text URLs
            $originalContent = $content;
            $content = str_replace('https://' . $backendDomain, 'https://' . $frontendDomain, $content);
            $content = str_replace('http://' . $backendDomain, 'http://' . $frontendDomain, $content);

            if ($content !== $originalContent) {
                $this->logger->info('EMAIL_FLOW_STEP_58: Plain text URLs were rewritten for domain: ' . $backendDomain);
                $urlsRewritten = true;
            }
        }

        if ($urlsRewritten) {
            $this->logger->info('EMAIL_FLOW_STEP_59: SUCCESS - At least one URL was rewritten');
        } else {
            $this->logger->info('EMAIL_FLOW_STEP_60: No URLs were rewritten (no backend domains found)');
        }

        return $content;
    }
}
