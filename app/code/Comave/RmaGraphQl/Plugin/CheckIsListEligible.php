<?php

declare(strict_types=1);

namespace Comave\RmaGraphQl\Plugin;

use Comave\Rma\Service\OrderGracePeriod;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Magento\RmaGraphQl\Model\Resolver\CustomerOrder\EligibleItems;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Api\Data\OrderItemInterface;
use Psr\Log\LoggerInterface;

class CheckIsListEligible
{
    /**
     * @param OrderGracePeriod $orderGracePeriod
     * @param TimezoneInterface $dateTime
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly OrderGracePeriod $orderGracePeriod,
        private readonly TimezoneInterface $dateTime,
        private readonly LoggerInterface $logger,
    ) {
    }

    /**
     * @param EligibleItems $isEligibleResolver
     * @param OrderItemInterface[]|array $eligibleReturnItems
     * @param Field $field
     * @param $context
     * @param ResolveInfo $info
     * @param array|null $value
     * @return OrderItemInterface[]|array
     */
    public function afterResolve(
        EligibleItems $isEligibleResolver,
        array $eligibleReturnItems,
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null
    ): array {
        if (!isset($value['model']) && !($value['model'] instanceof OrderInterface)) {
            return $eligibleReturnItems;
        }

        if (empty($eligibleReturnItems)) {
            return $eligibleReturnItems;
        }

        try {
            $isOrderWithinPeriod = $this->orderGracePeriod->isAllowed($value['model']);
            return $isOrderWithinPeriod === true ? $eligibleReturnItems : [];
        } catch (\Exception $e) {
            $this->logger->warning(
                '[ComaveRmaEligibility] Failed checking grace period for RMA, falling back to 14 days from order creation',
                [
                    'message' => $e->getMessage(),
                    'order' => $value['model']->getIncrementId(),
                ]
            );
        }

        $currentDate = $this->dateTime->date()->setTime(0, 0);
        $gracePeriodDate = $this->dateTime->date(
            strtotime($value['model']->getCreatedAt())
        )->setTime(0, 0)
            ->add(
                new \DateInterval(sprintf('P%dD', 14))
            );

        return $currentDate->getTimestamp() >= $gracePeriodDate->getTimestamp() ?
            $eligibleReturnItems : [];
    }
}
