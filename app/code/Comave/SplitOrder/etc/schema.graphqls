type Cart @doc(description: "Contains the contents and other details about a guest or customer cart.") {
    sellerItems(
        pageSize: Int = 20,
        currentPage: Int = 1,
        sort: QuoteItemsSortInput
    ): [SellerCartItems]
    @resolver(class: "\\Comave\\SplitOrder\\Model\\Resolver\\CartItemsPaginated")
}

type Seller {
    shipping_information: SellerCartShippingInformation @doc(description: "Information about the seller address")
}

type SellerCartItems {
    sellerDetails: Seller! @doc(description: "Seller information")
    items: CartItems @doc(description: "List of seller items in the basket")
}

type SellerCartShippingInformation {
    service_name: String! @doc (description: "Service name for the current seller shipping method")
    price: Money! @doc(description: "Total Price for the given shipping method")
    total_lead_time: Int @doc(description: "Shipping Lead Time")
    total_weight: Float @doc(description: "Overall calculated weight of seller items")
}
