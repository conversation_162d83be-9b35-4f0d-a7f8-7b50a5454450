<?php

declare(strict_types=1);

namespace Comave\SellerApi\Api;

use Magento\Framework\Api\ExtensibleDataInterface;

interface IntegrationInterface extends ExtensibleDataInterface
{
    public const string TABLE_MARKETPLACE_PRODUCTS_FLAT = 'comave_marketplace_products_flat';

    /**
     * @return string
     */
    public function getIntegrationType(): string;

    /**
     * @return string
     */
    public function getTableLink(): string;

    /**
     * @return string
     */
    public function getSellerColumnIdentifier(): string;

    /**
     * @return string
     */
    public function getSellerColumnProduct(): string;

    /**
     * @param string $sellerId
     * @return self
     */
    public function setSellerId(string $sellerId): self;

    /**
     * @return string|null
     */
    public function getSellerId(): ?string;

    /**
     * @param string $integrationType
     * @return self
     */
    public function setIntegrationType(string $integrationType): self;
}
