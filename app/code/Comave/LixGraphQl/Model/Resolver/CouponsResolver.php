<?php
declare(strict_types=1);

namespace Comave\LixGraphQl\Model\Resolver;

use Comave\LixApiConnector\Request\CouponRequest;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
class CouponsResolver implements ResolverInterface
{
    public function __construct(
        CouponRequest $couponRequest,
    ) {
    }

    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ): array
    {
        $result = [];
        foreach ([] as $lixTask) {
            $result[] = [
                'id' => $lixTask->getTaskId(),
                'code' => $lixTask->getTitle(),
                'title' => $lixTask->getTitle(),
                'description' => $lixTask->getTitle(),
                'amount' => $lixTask->getTitle(),
                'type' => $lixTask->getTitle(),
                'status' => $lixTask->getTitle(),
            ];
        }

        return $result;
    }
}
