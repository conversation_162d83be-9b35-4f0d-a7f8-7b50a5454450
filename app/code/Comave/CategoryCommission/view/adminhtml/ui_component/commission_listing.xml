<?xml version="1.0" encoding="UTF-8"?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">commission_listing.commission_listing_data_source</item>
        </item>
    </argument>
    <settings>
        <spinner>commission_columns</spinner>
        <deps>
            <dep>commission_listing.commission_listing_data_source</dep>
        </deps>
        <buttons>
            <button name="Upload Commissions CSV" class="Comave\CategoryCommission\Block\Adminhtml\Buttons\RedirectUploadPage">
            </button>
        </buttons>
    </settings>
    <dataSource name="commission_listing_data_source" component="Magento_Ui/js/grid/provider">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="component" xsi:type="string">Magento_Ui/js/grid/provider</item>
                <item name="filter_url_params" xsi:type="array">
                    <item name="entity_id" xsi:type="string">*</item>
                </item>
                <item name="update_url" xsi:type="url" path="mui/index/render"/>
                <item name="storageConfig" xsi:type="array">
                    <item name="cacheRequests" xsi:type="boolean">false</item>
                </item>
            </item>
        </argument>
        <settings>
            <storageConfig>
                <param name="indexField" xsi:type="string">entity_id</param>
            </storageConfig>
            <filterUrlParams>
                <param name="entity_id">*</param>
            </filterUrlParams>
            <updateUrl path="mui/index/render"/>
        </settings>
        <dataProvider class="Comave\CategoryCommission\Ui\Component\Listing\Commission\DataProvider" name="commission_listing_data_source">
            <settings>
                <requestFieldName>id</requestFieldName>
                <primaryFieldName>entity_id</primaryFieldName>
            </settings>
        </dataProvider>
    </dataSource>
    <listingToolbar name="listing_top">
        <exportButton class="Magento\Ui\Component\ExportButton">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="options" xsi:type="array">
                        <item name="csv" xsi:type="array">
                            <item name="url" xsi:type="string">commission/export/download/csv/1</item>
                        </item>
                        <item name="xml" xsi:type="array">
                            <item name="url" xsi:type="string">commission/export/download/xls/1</item>
                        </item>
                    </item>
                    <item name="selectProvider" xsi:type="string">commission_listing.commission_listing.commission_columns.ids</item>
                </item>
            </argument>
        </exportButton>
        <bookmark name="bookmarks"/>
        <columnsControls name="columns_controls"/>
        <filterSearch name="fulltext"/>
        <filters name="listing_filters">
            <settings>
                <templates>
                    <filters>
                        <select>
                            <param name="template" xsi:type="string">ui/grid/filters/elements/ui-select</param>
                            <param name="component" xsi:type="string">Magento_Ui/js/form/element/ui-select</param>
                        </select>
                    </filters>
                </templates>
            </settings>
            <filterSelect name="store_id" provider="${ $.parentName }">
                <settings>
                    <captionValue>0</captionValue>
                    <options class="Comave\CategoryCommission\Ui\Component\Listing\Column\Options"/>
                    <label translate="true">Store View</label>
                    <dataScope>store_id</dataScope>
                    <imports>
                        <link name="visible">componentType = column, index = ${ $.index }:visible</link>
                    </imports>
                </settings>
            </filterSelect>
        </filters>
        <massaction name="listing_massaction">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="selectProvider" xsi:type="string">commission_listing.commission_listing.commission_columns.ids</item>
                    <item name="indexField" xsi:type="string">ids</item>
                </item>
            </argument>
            <action name="delete">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="type" xsi:type="string">delete</item>
                        <item name="label" xsi:type="string" translate="true">Delete</item>
                        <item name="url" xsi:type="url" path="commission/index/massDelete"/>
                        <item name="confirm" xsi:type="array">
                            <item name="title" xsi:type="string" translate="true">Delete items</item>
                            <item name="message" xsi:type="string" translate="true">Are you sure you wan't to delete selected items?</item>
                        </item>
                    </item>
                </argument>
            </action>

        </massaction>

        <paging name="listing_paging"/>
    </listingToolbar>
    <columns name="commission_columns">
        <settings>
            <editorConfig>
                <param name="clientConfig" xsi:type="array">
                    <item name="saveUrl" xsi:type="url" path="commission/index/inlineEdit"/>
                    <item name="validateBeforeSave" xsi:type="boolean">false</item>
                </param>
                <param name="indexField" xsi:type="string">entity_id</param>
                <param name="enabled" xsi:type="boolean">true</param>
                <param name="selectProvider" xsi:type="string">commission_listing.commission_listing.commission_columns.ids</param>
            </editorConfig>
            <childDefaults>
                <param name="fieldAction" xsi:type="array">
                    <item name="provider" xsi:type="string">commission_listing.commission_listing.commission_columns_editor</item>
                    <item name="target" xsi:type="string">startEdit</item>
                    <item name="params" xsi:type="array">
                        <item name="0" xsi:type="string">${ $.$data.rowIndex }</item>
                        <item name="1" xsi:type="boolean">true</item>
                    </item>
                </param>
            </childDefaults>
        </settings>
        <selectionsColumn name="ids" sortOrder="0">
            <settings>
                <indexField>entity_id</indexField>
                <preserveSelectionsOnFilter>true</preserveSelectionsOnFilter>
            </settings>
        </selectionsColumn>
        <column name="entity_id">
            <settings>
                <filter>textRange</filter>
                <label translate="true">Commission ID</label>
                <sorting>asc</sorting>
            </settings>
        </column>
        <column name="category_id">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="editor" xsi:type="array">
                        <item name="editorType" xsi:type="string">text</item>
                        <item name="validation" xsi:type="array">
                            <item name="required-entry" xsi:type="boolean">true</item>
                        </item>
                    </item>
                    <item name="filter" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Category ID</item>
                    <item name="sortOrder" xsi:type="number">10</item>
                </item>
            </argument>
        </column>
        <column name="category_name">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="editor" xsi:type="array">
                        <item name="editorType" xsi:type="string">text</item>
                        <item name="validation" xsi:type="array">
                            <item name="required-entry" xsi:type="boolean">true</item>
                        </item>
                    </item>
                    <item name="filter" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Category Name</item>
                    <item name="sortOrder" xsi:type="number">20</item>
                </item>
            </argument>
        </column>
        <column name="store_id" component="Magento_Ui/js/grid/columns/select">
            <settings>
                <filter>select</filter>
                <editor>
                    <validation>
                        <rule name="required-entry" xsi:type="boolean">true</rule>
                    </validation>
                    <editorType>select</editorType>
                </editor>
                <options class="Comave\CategoryCommission\Ui\Component\Listing\Column\Options"/>
                <dataType>select</dataType>
                <label translate="true">Store View</label>
            </settings>
        </column>
        <column name="type" component="Magento_Ui/js/grid/columns/select">
            <settings>
                <filter>select</filter>
                <editor>
                    <validation>
                        <rule name="required-entry" xsi:type="boolean">true</rule>
                    </validation>
                    <editorType>select</editorType>
                </editor>
                <options class="Comave\CategoryCommission\Model\Source\CommissionType"/>
                <dataType>select</dataType>
                <label translate="true">Type</label>
            </settings>
        </column>
        <column name="value">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="editor" xsi:type="array">
                        <item name="editorType" xsi:type="string">text</item>
                        <item name="validation" xsi:type="array">
                            <item name="required-entry" xsi:type="boolean">true</item>
                        </item>
                    </item>
                    <item name="filter" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Value</item>
                    <item name="sortOrder" xsi:type="number">50</item>
                </item>
            </argument>
        </column>
    </columns>
</listing>
