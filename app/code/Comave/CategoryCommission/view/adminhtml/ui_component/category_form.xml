<?xml version="1.0" encoding="UTF-8"?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <fieldset name="general">
        <field name="commission_type">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Comave\CategoryCommission\Model\Source\CommissionType</item>
                <item name="config" xsi:type="array">
                    <item name="sortOrder" xsi:type="number">49</item>
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="formElement" xsi:type="string">select</item>
                    <item name="label" xsi:type="string" translate="true">Commission Type</item>
                    <item name="default" xsi:type="number">0</item>
                </item>
            </argument>
        </field>
        <field name="commission_value">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="sortOrder" xsi:type="number">50</item>
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="formElement" xsi:type="string">input</item>
                    <item name="label" xsi:type="string" translate="true">Commission Value</item>
                    <item name="default" xsi:type="number">10</item>
                </item>
            </argument>
        </field>
    </fieldset>
</form>
