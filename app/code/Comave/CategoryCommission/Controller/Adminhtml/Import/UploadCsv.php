<?php
declare(strict_types=1);

namespace Comave\CategoryCommission\Controller\Adminhtml\Import;

use Comave\CategoryCommission\Model\CommissionFactory;
use Magento\Backend\App\Action\Context;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\File\Csv;
use Magento\MediaStorage\Model\File\UploaderFactory;
use Comave\CategoryCommission\Api\CommissionRepositoryInterface;
use Psr\Log\LoggerInterface;
use Magento\Framework\Filesystem;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\UrlInterface;
use Magento\Framework\Controller\ResultFactory;

class UploadCsv extends \Magento\Backend\App\Action
{
    /**
     * @param Csv $csvProcessor
     * @param UploaderFactory $fileUploaderFactory
     * @param Context $context
     * @param CommissionRepositoryInterface $commissionRepository
     * @param CommissionFactory $commissionFactory
     * @param StoreManagerInterface $storeManager
     * @param Filesystem $filesystem
     */
    public function __construct(
        private readonly Csv                           $csvProcessor,
        private readonly UploaderFactory               $fileUploaderFactory,
        private readonly Context                       $context,
        private readonly CommissionRepositoryInterface $commissionRepository,
        private readonly CommissionFactory             $commissionFactory,
        private readonly StoreManagerInterface         $storeManager,
        private readonly Filesystem                    $filesystem

    ) {
        parent::__construct($context);
    }

    public function execute()
    {
        $jsonResult = $this->resultFactory->create(ResultFactory::TYPE_JSON);
        try {
            $fileUploader = $this->fileUploaderFactory->create(['fileId' => 'commission_file']);
            $fileUploader->setAllowedExtensions(['csv']);
            $fileUploader->setAllowRenameFiles(true);
            $fileUploader->setAllowCreateFolders(true);
            $fileUploader->setFilesDispersion(false);
            $fileUploader->validateFile();
            $data = $this->getRequest()->getFiles('commission_file');
            $tempFile = $data['tmp_name'];
            $importData = $this->csvProcessor->getData($tempFile);
            $lines = 0;
            $errorCount = 0;
            $errorMessage = '';

            foreach ($importData as $rowIndex => $dataRow) {
                if ($rowIndex == 0) {
                    if (!$this->getValidateColumns($dataRow)) {
                        return $jsonResult->setData(['errorcode' => 0, 'error' => __('The file does not contain all the required fields.
                         Please download the current export, make the required changes, and upload the same file')]);
                    }
                    continue;
                }
                try {
                    if ($dataRow[0]) {
                        $commission = $this->commissionRepository->getById($dataRow[0]);
                    } else {
                        $commission = $this->commissionFactory->create();
                    }
                    $commission->setCategoryId($dataRow[1]);
                    $commission->setCategoryName($dataRow[2]);
                    $commission->setStoreId($dataRow[3]);
                    $commission->setType($dataRow[4]);
                    $commission->setValue($dataRow[5]);
                    $this->commissionRepository->save($commission);
                } catch (LocalizedException $e) {
                    $errorCount++;
                    $errorMessage .= 'Error on line ' . $lines."\n";
                    if ($errorCount > 10) {
                        return $jsonResult->setData(['errorcode' => 0, 'error' => __($errorMessage)]);
                    }
                }
                $lines++;
            }
        } catch (\Exception $e) {
                return $jsonResult->setData(['errorcode' => 0, 'error' => __($e->getMessage())]);
        }
        $mediaDirectory = $this->filesystem->getDirectoryWrite(\Magento\Framework\App\Filesystem\DirectoryList::MEDIA);
        $result = $fileUploader->save($mediaDirectory->getAbsolutePath('tmp/csvUpload/category'));
        $result['url'] = $this->storeManager->getStore()->getBaseUrl(UrlInterface::URL_TYPE_MEDIA) . 'regenerate_images/';
        return $jsonResult->setData($result);
    }

    /**
     * @param array $header
     * @return bool
     */
    private function getValidateColumns(array $header): bool
    {
        if(count($header) !== 6){
            return false;
        }
        return true;
    }
}
