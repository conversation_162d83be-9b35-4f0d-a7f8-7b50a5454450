<?php
declare(strict_types=1);

namespace Comave\CategoryCommission\Model;

use Comave\CategoryCommission\Api\CommissionRepositoryInterface;
use Comave\CategoryCommission\Api\Data\CommissionSearchResultsInterface;
use Magento\Framework\Api\ExtensionAttribute\JoinProcessorInterface;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Comave\CategoryCommission\Api\Data\CommissionInterface;
use Comave\CategoryCommission\Api\Data\CommissionSearchResultsInterfaceFactory;
use Comave\CategoryCommission\Model\ResourceModel\Commission\CollectionFactory as CommissionCollectionFactory;
use Comave\CategoryCommission\Model\ResourceModel\Commission as CommissionResource;
use Magento\Catalog\Api\CategoryRepositoryInterface;
use Psr\Log\LoggerInterface;
/**
 * Class CommissionRepository
 */
class CommissionRepository implements CommissionRepositoryInterface
{

    /**
     * CommissionRepository constructor.
     * @param CommissionFactory $commissionFactory
     * @param CommissionCollectionFactory $commissionCollectionFactory
     * @param CommissionSearchResultsInterfaceFactory $commissionSearchResultsInterfaceFactory
     * @param JoinProcessorInterface $joinProcessor
     * @param CollectionProcessorInterface $collectionProcessor
     *
     */
    public function __construct(
        private readonly CommissionFactory $commissionFactory,
        private readonly CommissionCollectionFactory $commissionCollectionFactory,
        private readonly CommissionSearchResultsInterfaceFactory $commissionSearchResultsInterfaceFactory,
        private readonly JoinProcessorInterface $joinProcessor,
        private readonly CollectionProcessorInterface $collectionProcessor,
        private readonly CategoryRepositoryInterface $categoryRepository,
        protected LoggerInterface $logger

    ) {
    }

    /**
     * @param int $commissionId
     * @return CommissionInterface
     * @throws NoSuchEntityException
     */
    public function getById($commissionId): CommissionInterface
    {
        $commission = $this->commissionFactory->create();
        $commission->getResource()->load($commission, $commissionId);
        if (!$commission->getId()) {
            throw new NoSuchEntityException(__('Unable to find Commission with ID "%1"', $commissionId));
        }
        return $commission;
    }

    /**
     * @param int $categoryId
     * @param bool $generateEntry
     * @param string $categoryName
     * @param int $storeId
     * @return array|CommissionInterface[]
     * @throws NoSuchEntityException
     */
    public function getByCategoryId(int $categoryId, bool $generateEntry = false, string $categoryName = '', int $storeId = 0): array
    {
        $collection = $this->commissionCollectionFactory->create();
        $select = $collection->getSelect()
            ->where(CommissionInterface::CATEGORY_ID . ' = ?', $categoryId)
            ->where(CommissionInterface::STORE_ID . ' = ?', $storeId);
        $results = $collection->getConnection()->fetchAll($select);
        $commissions = [];
        foreach($results as $commission) {
            $commissions[] = $this->getById($commission[CommissionInterface::ENTITY_ID]);
        }
        //create default category commission entity on all stores views, type percent, value 10
        if (!isset($commissions[0]) && $generateEntry) {
                $commission = $this->commissionFactory->create();
                $commission->setCategoryId($categoryId);
                $commission->setStoreId($storeId);
                $commission->setCategoryName($categoryName);
                $commission->setType(1);
                $commission->setValue(CommissionInterface::COMMISSION_PERCENT_VALUE);
                $this->save($commission);
                $commissions[] = $commission;
        }

        return $commissions;
    }


    /**
     * @param int $entityId
     * @return CommissionResource
     */
    public function deleteById(int $entityId): CommissionResource
    {
        $commission = $this->commissionFactory->create();
        $commission->getResource()->load($commission, $entityId);
        return  $this->delete($commission);
    }

    /**
     * @param $value
     * @param $attributeCode
     * @return Commission
     * @throws NoSuchEntityException
     */
    public function get($value, $attributeCode = null): CommissionInterface
    {
        $commission = $this->commissionFactory->create();
        $commission->getResource()->load($commission, $value, $attributeCode);
        if (!$commission->getId()) {
            throw new NoSuchEntityException(
                __('The model with the "%1" "%2" wasn\'t found. Verify the ID and try again.', $value, $attributeCode)
            );
        }
        return $commission;
    }

    /**
     * @param CommissionInterface $commission
     * @return CommissionInterface
     */
    public function save(CommissionInterface $commission): CommissionInterface
    {
        $commission->getResource()->save($commission);
        return $commission;
    }

    /**
     * @param CommissionInterface $commission
     * @return CommissionResource
     * @throws \Exception
     */
    public function delete(CommissionInterface $commission): CommissionResource
    {
       return $commission->getResource()->delete($commission);
    }

    /**
     * @param SearchCriteriaInterface $searchCriteria
     * @return \Magento\Framework\Api\SearchResults
     */
    public function getList(SearchCriteriaInterface $searchCriteria): \Magento\Framework\Api\SearchResults
    {
        $collection = $this->commissionCollectionFactory->create();

        $this->joinProcessor->process($collection);
        $this->collectionProcessor->process($searchCriteria, $collection);

        $commission = [];
        foreach ($collection->getItems() as $items) {
            $commission[$items->getId()] = $items->getData();
        }

        $searchResults = $this->commissionSearchResultsInterfaceFactory->create();
        $searchResults->setSearchCriteria($searchCriteria);
        $searchResults->setItems($commission);
        $searchResults->setTotalCount($collection->getSize());

        return $searchResults;
    }
}
