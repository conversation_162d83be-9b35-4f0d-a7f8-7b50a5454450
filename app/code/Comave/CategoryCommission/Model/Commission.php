<?php
declare(strict_types=1);

namespace Comave\CategoryCommission\Model;

use Comave\CategoryCommission\Api\Data\CommissionExtensionInterface;
use Comave\CategoryCommission\Api\Data\CommissionInterface;
use Magento\Framework\Model\AbstractExtensibleModel;

/**
 * Class Commission
 */
class Commission extends AbstractExtensibleModel implements CommissionInterface
{

    /**
     * @return void
     */
    protected function _construct()
    {
        $this->_init(\Comave\CategoryCommission\Model\ResourceModel\Commission::class);
    }

    /**
     * @return int|null
     */
    public function getStoreId(): ?int
    {
        return (int)$this->getData(self::STORE_ID);
    }

    /**
     * @param int $storeId
     * @return void
     */
    public function setStoreId($storeId)
    {
        $this->setData(self::STORE_ID, $storeId);
    }

    /**
     * @return int|null
     */
    public function getCategoryId(): ?int
    {
        return (int)$this->getData(self::CATEGORY_ID);
    }

    /**
     * @param $categoryId
     * @return void
     */
    public function setCategoryId($categoryId)
    {
        $this->setData(self::CATEGORY_ID, $categoryId);
    }

    /**
     * @return string|null
     */
    public function getCategoryName(): ?string
    {
        return $this->getData(self::CATEGORY_NAME);

    }

    /**
     * @param $categoryName
     * @return void
     */
    public function setCategoryName($categoryName)
    {
        $this->setData(self::CATEGORY_NAME, $categoryName);
    }

    /**
     * @return int|null
     */
    public function getEntityId():  ?int
    {
        return (int)$this->getData(self::ENTITY_ID);
    }

    /**
     * @param $entityId
     * @return void
     */
    public function setEntityId($entityId)
    {
        $this->setData(self::ENTITY_ID, $entityId);
    }

    /**
     * @return int|null
     */
    public function getType(): ?int
    {
        return (int)$this->getData(self::TYPE);
    }

    /**
     * @param $type
     * @return void
     */
    public function setType($type)
    {
        $this->setData(self::TYPE, $type);
    }

    /**
     * @return int|null
     */
    public function getValue(): ?int
    {
        $value = $this->getData(self::VALUE);

        return !empty($value) ? (int) $value : null;
    }

    /**
     * @param $value
     * @return void
     */
    public function setValue($value)
    {
        $this->setData(self::VALUE, $value);
    }

    /**
     * @return AbstractModel
     */
    public function beforeSave()
    {
        if ($this->hasDataChanges()) {
            $this->setUpdateTime(null);
        }

        return parent::beforeSave();
    }

    /**
     * @return CommissionExtensionInterface|\Magento\Framework\Api\ExtensionAttributesInterface|null
     */
    public function getExtensionAttributes(): ?CommissionExtensionInterface
    {
        return $this->_getExtensionAttributes();
    }

    /**
     * @param CommissionExtensionInterface $extensionAttributes
     * @return Commission
     */
    public function setExtensionAttributes(CommissionExtensionInterface $extensionAttributes): Commission
    {
        return $this->_setExtensionAttributes($extensionAttributes);
    }
}
