<?php
declare(strict_types=1);
namespace Comave\CategoryCommission\Plugin\Catalog\Model\Category;

use Comave\CategoryCommission\Api\CommissionRepositoryInterface;

/**
 * Class Load
 */
class PluginLoadCategory
{

    /**
     * Get constructor.
     * @param CommissionRepositoryInterface $commissionRepository
     */
    public function __construct(
        private    CommissionRepositoryInterface $commissionRepository
    ) {
    }

    /**
     * @param \Magento\Catalog\Api\Data\CategoryInterface $subject
     * @param \Magento\Catalog\Api\Data\CategoryInterface $entity
     * @return \Magento\Catalog\Api\Data\CategoryInterface
     */
    public function afterLoad(\Magento\Catalog\Api\Data\CategoryInterface $subject, \Magento\Catalog\Api\Data\CategoryInterface $entity)
    {

        $commission = $this->commissionRepository->getByCategoryId(
            (int)$entity->getEntityId(),
            true,
            $entity->getName() ?: ''
        );
        if (isset($commission[0])){
            $subject->setData('commission_type', $commission[0]->getType());
            $subject->setData('commission_value', $commission[0]->getValue());
        }
        $extensionAttributes = $subject->getExtensionAttributes();
        /** get current extension attributes from entity **/
        $extensionAttributes->setCommission($commission);
        $subject->setExtensionAttributes($extensionAttributes);

        return $subject;
    }

    /**
     * @param \Magento\Catalog\Api\Data\CategoryInterface $subject
     * @param \Magento\Catalog\Api\Data\CategoryInterface $entity
     * @return \Magento\Catalog\Api\Data\CategoryInterface
     */
    public function afterSave(\Magento\Catalog\Api\Data\CategoryInterface $subject, \Magento\Catalog\Api\Data\CategoryInterface $entity)
    {
        if(!$subject->getCommissionType() || !$subject->getCommissionValue()){
            return $subject;
        }
        $commissions = $subject->getExtensionAttributes()->getCommission();
        foreach ($commissions as $commission) {
            if($commission->getStoreId() == $subject->getStoreId()){
                $commission->setType($subject->getCommissionType());
                $commission->setValue($subject->getCommissionValue());

                $commission->setStoreId($subject->getStoreId());
                $this->commissionRepository->save($commission);
            }
        }
        return $subject;
    }
}
