<?php
declare(strict_types=1);
namespace Comave\CategoryCommission\Plugin\Catalog\Model\Product;

use Comave\CategoryCommission\Api\CommissionRepositoryInterface;
use Comave\CategoryCommission\Api\Data\CommissionInterface;
class PluginSaveProduct
{

    /**
     * Get constructor.
     * @param CommissionRepositoryInterface $commissionRepository
     */
    public function __construct(
        private    CommissionRepositoryInterface $commissionRepository
    ) {
    }

    /**
     * @param \Magento\Catalog\Api\Data\ProductInterface $subject
     * @return \Magento\Catalog\Api\Data\ProductInterface
     */
    public function beforeSave(\Magento\Catalog\Api\Data\ProductInterface $subject)
    {
        if($subject->getCommissionForProduct()){
            return $subject;
        }
        $categoryIds = $subject->getCategoryIds();
        //default commission 10% of product price (in case no category is assigned to it)
        $defaultCommissionValue = $subject->getPrice() * CommissionInterface::COMMISSION_PERCENT_VALUE / 100;

        //try and pick lowest commission
        foreach ($categoryIds as $categoryId) {
            $categoryCommission = $this->commissionRepository->getByCategoryId((int)$categoryId);
            if (!empty($categoryCommission)) {
                if ($categoryCommission[0]->getType() == 0 && $categoryCommission[0]->getValue() < $defaultCommissionValue) {
                    $defaultCommissionValue = $categoryCommission[0]->getValue();
                } else {
                    if (($categoryCommission[0]->getValue() * $subject->getPrice() / 100) < $defaultCommissionValue) {
                        $defaultCommissionValue = $categoryCommission[0]->getValue();
                    }
                }
            }
        }
        $subject->setCommissionForProduct($defaultCommissionValue);
        return $subject;
    }
}
