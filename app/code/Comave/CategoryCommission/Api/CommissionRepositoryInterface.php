<?php
declare(strict_types=1);

namespace Comave\CategoryCommission\Api;

use Comave\CategoryCommission\Api\Data\CommissionInterface;
use Comave\CategoryCommission\Api\Data\CommissionSearchResultsInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Api\SearchCriteriaInterface;
use Comave\CategoryCommission\Model\ResourceModel\Commission as CommissionResource;

interface CommissionRepositoryInterface
{

    /**
     * Delete Commission.
     *
     * @param CommissionInterface $commission
     * @return CommissionResource true on success
     * @throws LocalizedException
     */
    public function delete(CommissionInterface $commission): CommissionResource;

    /**
     * Delete Commission by ID.
     *
     * @param int $entityId
     * @return CommissionResource
     * @throws NoSuchEntityException
     * @throws LocalizedException
     */
    public function deleteById(int $entityId): CommissionResource;

    /**
     * Retrieve Commission.
     *
     * @param int $commissionId
     * @return CommissionInterface
     * @throws LocalizedException
     */
    public function getById(int $commissionId): CommissionInterface;

    /**
     * @param int $categoryId
     * @param bool $generateEntry
     * @param string $categoryName
     * @param int $storeId
     * @return array|CommissionInterface[]
     */
    public function getByCategoryId(int $categoryId, bool $generateEntry = false, string $categoryName = '', int $storeId = 0);

    /**
     * Retrieve Commission matching the specified criteria.
     *
     * @param SearchCriteriaInterface $searchCriteria
     * @return CommissionSearchResultsInterface
     * @throws LocalizedException
     */
    public function getList(SearchCriteriaInterface $searchCriteria);

    /**
     * Save Commission.
     *
     * @param CommissionInterface $commission
     * @return CommissionInterface
     * @throws LocalizedException
     */
    public function save(CommissionInterface $commission): CommissionInterface;
}
