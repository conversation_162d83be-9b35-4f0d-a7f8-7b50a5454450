<?php
declare(strict_types=1);

namespace Comave\EnhancedReviewGraphql\Service;

use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Api\CustomerMetadataInterface;
use Magento\Framework\UrlInterface;
use Magento\Framework\View\Asset\Repository as AssetRepository;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Filesystem;
use Magento\Framework\Filesystem\Directory\ReadInterface;
use Magento\MediaStorage\Helper\File\Storage as FileStorageHelper;
use Psr\Log\LoggerInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Exception\FileSystemException;

class CustomerAvatarService
{
    private const DEFAULT_AVATAR_PATH = 'Lof_CustomerAvatar::images/no-profile-photo.png';
    private const PROFILE_PICTURE_ATTRIBUTE = 'profile_picture';
    
    /**
     * @var array
     */
    private array $avatarUrlCache = [];
    
    /**
     * @var ReadInterface
     */
    private readonly ReadInterface $mediaDirectory;

    /**
     * @param CustomerRepositoryInterface $customerRepository
     * @param UrlInterface $urlBuilder
     * @param AssetRepository $assetRepo
     * @param Filesystem $filesystem
     * @param FileStorageHelper $fileStorageHelper
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly CustomerRepositoryInterface $customerRepository,
        private readonly UrlInterface $urlBuilder,
        private readonly AssetRepository $assetRepo,
        Filesystem $filesystem,
        private readonly FileStorageHelper $fileStorageHelper,
        private readonly LoggerInterface $logger
    ) {
        $this->mediaDirectory = $filesystem->getDirectoryRead(DirectoryList::MEDIA);
    }

    /**
     * Get customer avatar URL with caching
     *
     * @param int|null $customerId
     * @return string
     */
    public function getCustomerAvatarUrl(?int $customerId): string
    {
        if (!$customerId) {
            return $this->getDefaultAvatarUrl();
        }

        if (isset($this->avatarUrlCache[$customerId])) {
            return $this->avatarUrlCache[$customerId];
        }
        
        $avatarUrl = $this->getDefaultAvatarUrl();
        
        try {
            $customer = $this->customerRepository->getById($customerId);
            $profilePicture = $customer->getCustomAttribute(self::PROFILE_PICTURE_ATTRIBUTE);
            
            if ($profilePicture) {
                $file = $profilePicture->getValue();

                if ($file && $this->fileExists($file)) {
                    $mediaBase = $this->urlBuilder->getBaseUrl([
                        '_type' => UrlInterface::URL_TYPE_MEDIA
                    ]);
                    $relativePath = $this->buildRelativePath($file);
                    $avatarUrl = rtrim($mediaBase, '/') . '/' . $relativePath;
                }
            }
        } catch (NoSuchEntityException $e) {
            $this->logger->warning(
                'Customer not found when retrieving avatar',
                ['customer_id' => $customerId]
            );
        } catch (\Exception $e) {
            $this->logger->error(
                'Error retrieving customer avatar: ' . $e->getMessage(),
                ['customer_id' => $customerId, 'exception' => $e]
            );
        }

        $this->avatarUrlCache[$customerId] = $avatarUrl;        
        return $avatarUrl;
    }

    /**
     * Get default avatar URL
     * 
     * @return string
     */
    private function getDefaultAvatarUrl(): string
    {
        $asset = $this->assetRepo->createAsset(self::DEFAULT_AVATAR_PATH, ['area' => 'frontend']);
        return $asset->getUrl();
    }
    
    /**
     * Build relative path for customer avatar
     * 
     * @param string $file
     * @return string
     */
    private function buildRelativePath(string $file): string
    {
        return CustomerMetadataInterface::ENTITY_TYPE_CUSTOMER . '/' . ltrim($file, '/');
    }

    /**
     * Check if the customer profile picture file exists
     *
     * @param string $file
     * @return bool
     */
    private function fileExists(string $file): bool
    {
        try {
            $relativePath = $this->buildRelativePath($file);
            if ($this->mediaDirectory->isFile($relativePath)) {
                return true;
            }

            $absolutePath = $this->mediaDirectory->getAbsolutePath($relativePath);
            return $this->fileStorageHelper->processStorageFile($absolutePath);
            
        } catch (FileSystemException $e) {
            $this->logger->error(
                'Error checking file existence: ' . $e->getMessage(),
                ['file' => $file, 'exception' => $e]
            );
            return false;
        }
    }
}
