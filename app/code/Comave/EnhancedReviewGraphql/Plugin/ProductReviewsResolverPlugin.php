<?php
declare(strict_types=1);

namespace Comave\EnhancedReviewGraphql\Plugin;

use Comave\EnhancedReviewGraphql\Service\CustomerAvatarService;
use Magento\ReviewGraphQl\Model\Resolver\Product\Reviews as SubjectResolver;
use Magento\Review\Model\Review;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\Exception\NoSuchEntityException;

class ProductReviewsResolverPlugin
{
    private const CUSTOMER_UUID_ATTRIBUTE_CODE = 'commave_uuid';

    /**
     * @param CustomerAvatarService $customerAvatarService
     * @param CustomerRepositoryInterface $customerRepository
     */
    public function __construct(
        private CustomerAvatarService $customerAvatarService,
        private CustomerRepositoryInterface $customerRepository
    ) {}

    /**
     * @param SubjectResolver $subject
     * @param array $result
     * @return array
     */
    public function afterResolve(SubjectResolver $subject, array $result): array
    {
        if (empty($result['items']) || !is_array($result['items'])) {
            return $result;
        }

        foreach ($result['items'] as & $item) {
            $customerId = null;
            if (isset($item['model']) && $item['model'] instanceof Review) {
                $reviewModel = $item['model'];
                $customerId = $reviewModel->getCustomerId();
                $customerIdInt = $customerId ? (int)$customerId : null;                
                $comaveUuid = null;

                if ($customerId) {
                    $customer = $this->customerRepository->getById($customerIdInt);
                    $uuidAttribute = $customer->getCustomAttribute(self::CUSTOMER_UUID_ATTRIBUTE_CODE);
                    $comaveUuid = $uuidAttribute ? $uuidAttribute->getValue() : null;
                }
                
                $item['comave_uuid'] = $comaveUuid;
                $item['customer_avatar'] = $this->customerAvatarService->getCustomerAvatarUrl(
                    $customerIdInt
                );
            }
        }

        return $result;
    }
}