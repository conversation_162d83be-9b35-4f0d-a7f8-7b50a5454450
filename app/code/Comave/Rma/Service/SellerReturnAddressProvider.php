<?php

declare(strict_types=1);

namespace Comave\Rma\Service;

use Comave\SellerApi\Api\IntegrationInterface;
use Magento\Customer\Model\Customer;
use Magento\Eav\Api\AttributeRepositoryInterface;
use Magento\Framework\App\ResourceConnection;
use Magento\Sales\Api\Data\OrderInterface;

class SellerReturnAddressProvider
{
    /**
     * @param ResourceConnection $resourceConnection
     * @param AttributeRepositoryInterface $attributeRepository
     */
    public function __construct(
        private readonly ResourceConnection $resourceConnection,
        private readonly AttributeRepositoryInterface $attributeRepository
    ) {
    }

    /**
     * @param OrderInterface $order
     * @return mixed[]
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function get(OrderInterface $order): array
    {
        $connection = $this->resourceConnection->getConnection();
        $defaultReturnAddressAttribute = $this->attributeRepository->get(
            Customer::ENTITY,
            'default_return'
        );
        $sellerReturnAddressesSelect = $connection->select()
            ->from(
                ['flat' => IntegrationInterface::TABLE_MARKETPLACE_PRODUCTS_FLAT],
                []
            )->join(
                ['so' => $connection->getTableName('sales_order_item')],
                'so.product_id = flat.product_id',
                [
                    'item_id'
                ]
            )->join(
                ['s' => $connection->getTableName('sales_order')],
                's.entity_id = so.order_id',
                []
            )->join(
                ['ca' => $connection->getTableName('customer_address_entity')],
                'ca.parent_id = flat.seller_id'
            )->join(
                ['cae' => $connection->getTableName('customer_entity_int')],
                'ca.parent_id = cae.entity_id AND cae.value = ca.entity_id AND cae.attribute_id = ' . $defaultReturnAddressAttribute->getAttributeId(),
                []
            )->where(
                's.entity_id = ?',
                $order->getEntityId()
            )->where(
                'cae.value > ?',
                0
            );

        $returnAddresses = $connection->fetchAssoc($sellerReturnAddressesSelect) ?: [];

        if (empty($returnAddresses)) {
            //set backwards compatibility
            $sellerReturnAddressesSelect = $connection->select()
                ->from(
                    ['mp' => $connection->getTableName('marketplace_product')],
                    []
                )->join(
                    ['so' => $connection->getTableName('sales_order_item')],
                    'so.product_id = mp.mageproduct_id',
                    [
                        'item_id'
                    ]
                )->join(
                    ['s' => $connection->getTableName('sales_order')],
                    's.entity_id = so.order_id',
                    []
                )->join(
                    ['ca' => $connection->getTableName('customer_address_entity')],
                    'ca.parent_id = mp.seller_id'
                )->join(
                    ['cae' => $connection->getTableName('customer_entity_int')],
                    'ca.parent_id = cae.entity_id AND cae.value = ca.entity_id AND cae.attribute_id = ' . $defaultReturnAddressAttribute->getAttributeId(),
                    []
                )->where(
                    's.entity_id = ?',
                    $order->getEntityId()
                )->where(
                    'cae.value > ?',
                    0
                );

            $returnAddresses = $connection->fetchAssoc($sellerReturnAddressesSelect) ?: [];
        }

        return $returnAddresses;
    }
}
