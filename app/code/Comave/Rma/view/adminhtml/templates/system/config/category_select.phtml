<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" />
<?php
/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Magento\Framework\View\Helper\SecureHtmlRenderer $secureRenderer */
$elementId = $block->getElement()->getHtmlId();
$elementName = $block->getElement()->getName();
$elementValue = $block->getElement()->getValue() ?: [];
?>
<select
        multiple
        id="<?= $elementId ?>"
        name="<?= $elementName ?>[]"
        class="select admin__control-select select2-category">
    <?php foreach ($elementValue as $value) : ?>
        <?php $item = json_decode($value, true); ?>
        <option selected value="<?= htmlentities($value); ?>">
            <?= strip_tags($item['text']); ?>
        </option>
    <?php endforeach; ?>
</select>

<?php
$scriptString = <<<SCRIPTSTRING
require([
    'jquery',
    'underscore',
    'https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js'
], function ($, _) {
    $(document).ready(function() {
        $('#$elementId').select2({
            placeholder: "Select categories",
            minimumInputLength: 5,
            allowClear: true,
            ajax: {
                url: '{$block->getUrl('seller_onboarding/category_mapping/search')}',
                dataType: 'json',
                delay: 250,
                data: function (params) {
                    return {
                        q: params.term
                    };
                },
                processResults: function (data, params) {
                    params.page = params.page || 1;
                    return {
                        results: data.items, // Assuming your API returns an array of items
                        pagination: {
                            more: data.page < data.total_pages
                        }
                    };
                },
                cache: true
            }
        });

        setTimeout(function () {
            $('#$elementId').siblings('.select2.select2-container.select2-container--default').css({
                'width': '100%'
            });
        }, 600);

        const selectInput = $('#$elementId');

        selectInput.on('select2:select', function (e) {
            e.preventDefault();
            // Create and select the new option with JSON as value
            const item = e.params.data;
            const jsonVal = JSON.stringify({ id: item.id, text: item.text });

            //clear default values as select2 appends the numerical value and we don't want those
            selectInput.find('option').each(function () {
                const val = $(this).val();

                if (/^\d+$/.test(val) || val === jsonVal) {
                    $(this).remove();
                }
            });

            const option = new Option(item.text, jsonVal, true, true);
            selectInput.append(option).trigger('change');
        });
    });
});
SCRIPTSTRING;
?>
<?= $secureRenderer->renderTag('script', [], $scriptString, false); ?>
