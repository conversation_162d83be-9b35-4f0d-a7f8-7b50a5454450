<?php

declare(strict_types=1);

namespace Comave\Rma\Model\Queue\Consumer;

use Comave\Rma\Model\ConfigProvider;
use Magento\Catalog\Model\Product\Action;
use Magento\Framework\App\ResourceConnection;

class ProcessExclusions
{
    public const string TOPIC_NAME = 'rma.category.exclusion';

    /**
     * @param ConfigProvider $configProvider
     * @param ResourceConnection $resourceConnection
     * @param Action $productAction
     */
    public function __construct(
        private readonly ConfigProvider $configProvider,
        private readonly ResourceConnection $resourceConnection,
        private readonly Action $productAction
    ) {
    }

    /**
     * @param int $websiteId
     * @return void
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function process(int $websiteId = 0): void
    {
        $configValue = $this->configProvider->getExcludedCategories($websiteId);

        if (empty($configValue)) {
            return;
        }

        $categoryIds = [];

        foreach ($configValue as $categoryConfig) {
            $decoded = json_decode($categoryConfig, true);
            $categoryIds[] = $decoded['id'];
        }

        if (empty($categoryIds)) {
            return;
        }

        $connection = $this->resourceConnection->getConnection('read');
        $productIdsSelect = $connection->select()
            ->from(
                ['ccp' => $connection->getTableName('catalog_category_product')],
                []
            )->join(//this is to make sure products exist
                ['cpe' => $connection->getTableName('catalog_product_entity')],
                'cpe.entity_id = ccp.product_id',
                [
                    'entity_id'
                ]
            )->where(
                'category_id IN (?)',
                $categoryIds
            );

        $productIds = $connection->fetchCol($productIdsSelect);

        if (empty($productIds)) {
            return;
        }

        $this->productAction->updateAttributes(
            $productIds,
            [
                'is_returnable' => 0
            ],
            0
        );
    }
}
