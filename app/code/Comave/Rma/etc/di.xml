<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Rma\Helper\Eav">
        <plugin name="comaveOptionSource" type="Comave\Rma\Plugin\GetComaveOptionSource"/>
    </type>

    <type name="Comave\Rma\Plugin\GetComaveOptionSource">
        <arguments>
            <argument xsi:type="array" name="newOptionsProviders">
                <item name="reason" xsi:type="object">Comave\Rma\Model\Source\Reasons</item>
                <item name="resolution" xsi:type="object">Comave\Rma\Model\Source\Resolution</item>
            </argument>
        </arguments>
    </type>

    <type name="Magento\Framework\Console\CommandListInterface">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="dispatch_disable_rma" xsi:type="object">Comave\Rma\Console\Command\DisableCategoryProductsReturn</item>
            </argument>
        </arguments>
    </type>

    <type name="Magento\CustomAttributeManagement\Helper\Data">
        <plugin name="alphaWithHyphen" type="Comave\Rma\Plugin\AddValidationRule"/>
    </type>

    <virtualType name="ComaveSellerReturns" type="Comave\Logger\Model\ComaveLogger">
        <arguments>
            <argument name="name" xsi:type="string">ComaveReturns</argument>
            <argument name="loggerPath" xsi:type="string">returns</argument>
        </arguments>
    </virtualType>

    <type name="Magento\Rma\Api\Data\RmaInterface">
        <plugin name="setSellerInfo" type="Comave\Rma\Plugin\SetSellerOnRma"/>
    </type>

    <type name="Magento\Rma\Model\Rma\Source\Status">
        <plugin name="getComaveStatus" type="Comave\Rma\Plugin\ComaveStatusProvider"/>
    </type>
</config>
