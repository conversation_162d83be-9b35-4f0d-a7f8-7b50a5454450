<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework-message-queue:etc/consumer.xsd">
    <consumer name="rma.category.exclusion"
              queue="rma.category.exclusion"
              connection="amqp"
              onlySpawnWhenMessageAvailable="1"
              handler="Comave\Rma\Model\Queue\Consumer\ProcessExclusions::process"/>
</config>
