<?php

declare(strict_types=1);

namespace Comave\Rma\Block\Adminhtml\Form\Field;

use Magento\Framework\View\Element\Html\Select;
use Magento\Rma\Model\Rma\Source\Status;

class RmaStatuses extends Select
{
    /**
     * @param \Magento\Framework\View\Element\Context $context
     * @param Status $rmaStatus
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\View\Element\Context $context,
        private readonly Status $rmaStatus,
        array $data = []
    ) {
        parent::__construct($context, $data);
    }

    /**
     * @param string $value
     * @return self
     */
    public function setInputName(string $value): self
    {
        return $this->setName($value);
    }

    /**
     * @param string $value
     * @return $this
     */
    public function setInputId(string $value): self
    {
        return $this->setId($value);
    }

    /**
     *
     * @return string
     */
    public function _toHtml(): string
    {
        if (!$this->getOptions()) {
            $options = [];

            foreach (Status::STATE_ALL as $state) {
                $options[] = [
                    'value' => $state,
                    'label' => $this->rmaStatus->getItemLabel($state)
                ];
            }

            $this->_options = $options;
        }

        return parent::_toHtml();
    }
}
