<?php

declare(strict_types=1);

namespace Comave\Rma\Plugin;

use Magento\Framework\Data\OptionSourceInterface;

class GetComaveOptionSource
{
    /**
     * @param OptionSourceInterface[] $newOptionsProviders
     */
    public function __construct(private readonly array $newOptionsProviders)
    {
    }

    /**
     * @param \Magento\Rma\Helper\Eav $eavHelper
     * @param array $results
     * @return array
     */
    public function afterGetAttributeOptionStringValues(
        \Magento\Rma\Helper\Eav $eavHelper,
        array $results
    ): array {
        if (empty($this->newOptionsProviders)) {
            return $results;
        }

        $newOptions = [];

        foreach ($this->newOptionsProviders as $provider){
            foreach ($provider->toOptionArray() as $option) {
                $newOptions[$option['value']] = $option['label'];
            }
        }

        return $results + $newOptions;
    }

    /**
     * @param \Magento\Rma\Helper\Eav $eavHelper
     * @param array $results
     * @param string $attributeCode
     * @return array
     */
    public function afterGetAttributeOptionValues(
        \Magento\Rma\Helper\Eav $eavHelper,
        array $results,
        string $attributeCode,
    ): array {
        if (!isset($this->newOptionsProviders[$attributeCode])) {
            return $results;
        }

        $newOptions = $this->newOptionsProviders[$attributeCode]->toOptionArray();
        $results = [];

        foreach ($newOptions as $option) {
            $results[$option['value']] = $option['label'];
        }

        return $results;
    }
}
