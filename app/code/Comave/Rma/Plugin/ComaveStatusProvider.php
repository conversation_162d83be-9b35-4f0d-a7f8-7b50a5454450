<?php

declare(strict_types=1);

namespace Comave\Rma\Plugin;

class ComaveStatusProvider
{
    /**
     * @param \Comave\Rma\Model\Command\ComaveStatusProvider $comaveStatusProvider
     */
    public function __construct(private readonly \Comave\Rma\Model\Command\ComaveStatusProvider $comaveStatusProvider)
    {
    }

    /**
     * @param \Magento\Rma\Model\Rma\Source\Status $rmaStatusLabel
     * @param string $stateLabel
     * @param string $state
     * @return string
     */
    public function afterGetItemLabel(
        \Magento\Rma\Model\Rma\Source\Status $rmaStatusLabel,
        string $stateLabel,
        string $state,
    ): string {
        return $this->comaveStatusProvider->get($state) ?: $stateLabel;
    }
}
