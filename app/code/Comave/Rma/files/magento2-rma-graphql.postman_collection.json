{"info": {"name": "Magento 2 RMA GraphQL API", "_postman_id": "rma-graphql-example", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Request Return", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer <customer-token>"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"\\nmutation RequestReturn($input: RequestReturnInput!) {\\n  requestReturn(input: $input) {\\n    return {\\n      uid\\n      number\\n      status\\n    }\\n  }\\n}\\n\",\n  \"variables\": {\n    \"input\": {\n      \"order_uid\": \"order-uid-example\",\n      \"contact_email\": \"<EMAIL>\",\n      \"items\": [\n        {\n          \"order_item_uid\": \"item-uid-example\",\n          \"quantity_to_return\": 1\n        }\n      ],\n      \"comment_text\": \"Returning item due to defect\"\n    }\n  }\n}"}, "url": {"raw": "https://your-magento-site.com/graphql", "host": ["https://your-magento-site.com/graphql"]}}}, {"name": "Add Return Comment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer <customer-token>"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"\\nmutation AddReturnComment($input: AddReturnCommentInput!) {\\n  addReturnComment(input: $input) {\\n    return {\\n      uid\\n      comments {\\n        uid\\n        text\\n        created_at\\n      }\\n    }\\n  }\\n}\\n\",\n  \"variables\": {\n    \"input\": {\n      \"return_uid\": \"return-uid-example\",\n      \"comment_text\": \"Please expedite the process\"\n    }\n  }\n}"}, "url": {"raw": "https://your-magento-site.com/graphql", "host": ["https://your-magento-site.com/graphql"]}}}, {"name": "Add Return Tracking", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer <customer-token>"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"\\nmutation AddReturnTracking($input: AddReturnTrackingInput!) {\\n  addReturnTracking(input: $input) {\\n    return {\\n      uid\\n    }\\n    return_shipping_tracking {\\n      uid\\n      tracking_number\\n    }\\n  }\\n}\\n\",\n  \"variables\": {\n    \"input\": {\n      \"return_uid\": \"return-uid-example\",\n      \"carrier_uid\": \"carrier-uid-example\",\n      \"tracking_number\": \"TRACK123456789\"\n    }\n  }\n}"}, "url": {"raw": "https://your-magento-site.com/graphql", "host": ["https://your-magento-site.com/graphql"]}}}, {"name": "Remove Return Tracking", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer <customer-token>"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"\\nmutation RemoveReturnTracking($input: RemoveReturnTrackingInput!) {\\n  removeReturnTracking(input: $input) {\\n    return {\\n      uid\\n    }\\n  }\\n}\\n\",\n  \"variables\": {\n    \"input\": {\n      \"return_shipping_tracking_uid\": \"tracking-uid-example\"\n    }\n  }\n}"}, "url": {"raw": "https://your-magento-site.com/graphql", "host": ["https://your-magento-site.com/graphql"]}}}, {"name": "Get Returns List", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer <customer-token>"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"\\nquery {\\n  customer {\\n    returns {\\n      items {\\n        uid\\n        number\\n        status\\n      }\\n    }\\n  }\\n}\\n\"\n}"}, "url": {"raw": "https://your-magento-site.com/graphql", "host": ["https://your-magento-site.com/graphql"]}}}, {"name": "Get Return By UID", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer <customer-token>"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"\\nquery {\\n  customer {\\n    return(uid: \\\"return-uid-example\\\") {\\n      uid\\n      number\\n      status\\n      comments {\\n        text\\n        created_at\\n      }\\n    }\\n  }\\n}\\n\"\n}"}, "url": {"raw": "https://your-magento-site.com/graphql", "host": ["https://your-magento-site.com/graphql"]}}}]}