<?php

declare(strict_types=1);

namespace Comave\ShopifyAccounts\Model\Webhook\Processors;

use Comave\ShopifyAccounts\Api\WebhookTopicProcessorInterface;
use Comave\ShopifyAccounts\Api\WebhookValidatorInterface;
use Comave\ShopifyAccounts\Exception\InvalidWebhookRequestException;
use Comave\ShopifyAccounts\Model\Command\GetSellerByDomain;
use Comave\ShopifyAccounts\Model\Command\InventoryRegistry;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Framework\App\HttpRequestInterface;
use Magento\Framework\Serialize\SerializerInterface;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

class InventoryUpdate implements WebhookTopicProcessorInterface
{
    /**
     * @param ProductRepositoryInterface $productRepository
     * @param InventoryRegistry $inventoryRegistry
     * @param LoggerInterface $logger
     * @param SerializerInterface $serializer
     * @param GetSellerByDomain $getSellerByDomain
     * @param StoreManagerInterface $storeManager
     */
    public function __construct(
        private readonly ProductRepositoryInterface $productRepository,
        private readonly InventoryRegistry $inventoryRegistry,
        private readonly LoggerInterface $logger,
        private readonly SerializerInterface $serializer,
        private readonly GetSellerByDomain $getSellerByDomain,
        private readonly StoreManagerInterface $storeManager,
    ) {
    }

    public function process(HttpRequestInterface $request): void
    {
        $accountData = $this->getSellerByDomain->get(
            $request->getHeader(WebhookValidatorInterface::SELLER_DOMAIN_HEADER)
        );

        if (empty($accountData)) {
            throw new InvalidWebhookRequestException(__('Unable to identify seller'));
        }

        $accountId = $accountData['entity_id'];
        $json = $request->getContent();
        $inventoryArr = $this->serializer->unserialize($json);
        $inventoryItemId = $inventoryArr['inventory_item_id'] ?? false;

        if (empty($inventoryItemId)) {
            throw new InvalidWebhookRequestException(__('Unable to identify source of the stock'));
        }

        $this->logger->info(
            '[WebhookProcessor] Beginning inventory update',
            [
                'params' => $inventoryArr,
                'accountId' => $accountId,
            ]
        );

        try {
            $inventorySku = $this->inventoryRegistry->getSkuByInventoryId($inventoryItemId);

            if (empty($inventorySku)) {
                throw new InvalidWebhookRequestException(__('Unable to identify SKU for %1', $inventoryItemId));
            }

            $qty = (float) (
                max($inventoryArr['available'], 0)
            );
            $isInStock = $inventoryArr['available'] > 0;
            $product = $this->productRepository->get($inventorySku, true, 0, true);
            $this->storeManager->setCurrentStore(0);
            $product->setData(
                'quantity_and_stock_status',
                [
                    'qty' => $qty,
                    'is_in_stock' => $isInStock,
                ]
            );
            $product->save();
            $this->logger->info(
                '[WebhookProcessor] Finished inventory update',
                [
                    'accountId' => $accountId,
                ]
            );
        } catch (\Exception $e) {
            $this->logger->info(
                '[WebhookProcessor] Error during inventory update',
                [
                    'accountId' => $accountId,
                    'inventory_item' => $inventoryItemId,
                    'error' => $e->getMessage()
                ]
            );
        }
    }
}
