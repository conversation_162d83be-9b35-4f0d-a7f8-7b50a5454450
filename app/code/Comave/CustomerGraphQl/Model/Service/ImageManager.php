<?php
declare(strict_types=1);

namespace Comave\CustomerGraphQl\Model\Service;

use Comave\CustomerGraphQl\Model\Command\AvatarImagePathResolver;
use Magento\Framework\Api\ImageProcessorInterface;
use Magento\Framework\Api\Data\ImageContentInterfaceFactory;
use Magento\Framework\Exception\InputException;
use Magento\Framework\Exception\NoSuchEntityException;

class ImageManager
{
    /**
     * @param AvatarImagePathResolver $avatarImagePathResolver
     * @param ImageProcessorInterface $imageProcessor
     * @param ImageContentInterfaceFactory $imageContentFactory
     */
    public function __construct(
        private readonly AvatarImagePathResolver $avatarImagePathResolver,
        private readonly ImageProcessorInterface $imageProcessor,
        private readonly ImageContentInterfaceFactory $imageContentFactory
    ) {
    }

    /**
     * @param array $files
     * @param string $destinationFolder
     * @return array
     * @throws InputException
     * @throws NoSuchEntityException
     */
    public function processFile(array $files, string $destinationFolder): array
    {
        if (!count($files) ) {
            return [];
        }

        $results = [];

        foreach ($files as $file) {
            $imageContent = $this->imageContentFactory->create();
            $imageContent->setBase64EncodedData($file['base64_encoded_file']);
            $imageContent->setType($file['type']);
            $imageContent->setName($file['name']);
            $imagePath = $this->imageProcessor->processImageContent($destinationFolder, $imageContent);
            $results['full_path'] = $this->avatarImagePathResolver->resolve($imagePath);
            $results['attribute_value'] = $imagePath;
            $results['name'] = $file['name'];
        }

        return $results;
    }
}
