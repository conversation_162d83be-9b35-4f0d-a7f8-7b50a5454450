<?php
declare(strict_types=1);

namespace Comave\Integration\Setup\Patch\Data;

use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\App\Config\Storage\WriterInterface;

class DisableStaticContentSigning implements DataPatchInterface
{
    public function __construct(
        private readonly WriterInterface $configWriter
    ) {}

    /**
     * @return void
     */
    public function apply(): void
    {
        $this->configWriter->save('dev/static/sign', '0', 'default', 0);
    }

    /**
     * @return array
     */
    public static function getDependencies(): array
    {
        return [];
    }

    /**
     * @return array
     */
    public function getAliases(): array
    {
        return [];
    }
}
