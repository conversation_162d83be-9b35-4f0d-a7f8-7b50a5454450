<?php
//$helperBlock = $block->getLayout()->createBlock(\Webkul\MpEasyPost\Block\EasyPostData::class);
//$_helper = $helperBlock->getMarketplaceHelper();
//$helper = $helperBlock->getHelper();
//$isPartner = $_helper->isSeller();
//$isSellerGroup = $_helper->isSellerGroupModuleInstalled();
//if ($isPartner) { ?>
<!--    --><?php //if (($isSellerGroup && $_helper->isAllowedAction('returnaddress/index/index')) || !$isSellerGroup) { ?>
<!--        <li data-ui-id="menu-webkul-marketplace-setting-return-address" class="item-menu parent level-1">-->
<!--            <strong class="wk-mp-submenu-group-title">-->
<!--                <span>--><?php //= $block->escapeHtml(__('Returns')) ?><!--</span>-->
<!--            </strong>-->
<!--            <div class="wk-mp-submenu">-->
<!--                <ul>-->
<!--                    <li class="level-2">-->
<!--                        <a href="--><?php //= $block->escapeUrl($block->getUrl('returnaddress', ['_secure' => $block->getRequest()->isSecure()])); ?><!--">-->
<!--                            <span>--><?php //= $block->escapeHtml(__('Return Address')) ?><!--</span>-->
<!--                        </a>-->
<!--                    </li>-->
<!--                </ul>-->
<!--            </div>-->
<!--        </li>-->
<!--    --><?php //} ?>
<?php //} ?>

