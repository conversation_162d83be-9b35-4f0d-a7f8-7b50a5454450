<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Customer\Model\Customer">
        <plugin name="comave_customer_uuid_plugin" type="Comave\Customer\Plugin\CustomerUuidPlugin"/>
    </type>
    <type name="Magento\RewardGraphQl\Model\Formatter\Customer\Balance">
        <plugin name="comave_customer_model_formatter_customer_balance"
                type="Comave\Customer\Plugin\Model\Formatter\Customer\BalancePlugin"/>
    </type>

    <!-- Plugin to add the new alpha-with-hyphen validation option in the Admin Panel -->
    <type name="Magento\CustomerCustomAttributes\Helper\Data">
        <plugin name="add_alpha_hyphen_validation_rule" type="Comave\Customer\Plugin\CustomerCustomAttributes\AddValidationRule"/>
    </type>
    <!-- Plugin to allow the new alpha-with-hyphen validation option mapping per input types in the Admin Panel -->
    <type name="Magento\CustomAttributeManagement\Helper\Data">
        <plugin name="add_alpha_hyphen_validation_input" type="Comave\Customer\Plugin\CustomAttributeManagement\AddInputValidationRule"/>
    </type>
    <!-- Plugin to enforce validation when the rule is selected, for text and multiline(which extends text) -->
    <type name="Magento\Eav\Model\Attribute\Data\Text">
        <plugin name="validate_text_alpha_with_hyphen" type="Comave\Customer\Plugin\Eav\ValidateAlphaWithHyphen" />
    </type>
    <type name="Magento\Customer\Model\AccountManagement">
        <plugin name="comave_customer_model_account_management"
                type="Comave\Customer\Plugin\Model\AccountManagementPlugin"/>
    </type>
</config>
