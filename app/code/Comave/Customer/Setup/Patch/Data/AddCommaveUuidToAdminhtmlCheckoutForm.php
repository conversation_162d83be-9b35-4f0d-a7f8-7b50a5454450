<?php
declare(strict_types=1);

namespace Comave\Customer\Setup\Patch\Data;

use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Psr\Log\LoggerInterface;

class AddCommaveUuidToAdminhtmlCheckoutForm implements DataPatchInterface
{
    /**
     * @param ResourceConnection $resourceConnection
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly ResourceConnection $resourceConnection,
        private readonly LoggerInterface $logger,
    ) {}

    /**
     * @return void
     */
    public function apply(): void
    {
        $connection = $this->resourceConnection->getConnection();
        $eavTable = $connection->getTableName('eav_attribute');
        $formAttributeTable = $connection->getTableName('customer_form_attribute');

        $comaveUuidAttributeId = $connection->fetchOne(
            $connection->select('attribute_id')
                ->from(['eav' => $eavTable], ['attribute_id'])
                ->where('eav.attribute_code = ?', 'commave_uuid')
        );

        if (!$comaveUuidAttributeId) {
            return;
        }

        $exists = $connection->fetchOne(
            $connection->select('form_code')
                ->from($formAttributeTable)
                ->where('form_code = ?', 'adminhtml_checkout')
                ->where('attribute_id = ?', $comaveUuidAttributeId)
        );
        if (!$exists) {
            try {
                $connection->insert(
                    $formAttributeTable,
                    [
                        'form_code' => 'adminhtml_checkout',
                        'attribute_id' => $comaveUuidAttributeId,
                    ]
                );
            } catch (\Exception $e) {
                $this->logger->error($e->getMessage());
            }
        }

        return;
    }

    /**
     * @inheritDoc
     */
    public function getAliases(): array
    {
        return [];
    }

    /**
     * @inheritDoc
     */
    public static function getDependencies(): array
    {
        return [];
    }
}
