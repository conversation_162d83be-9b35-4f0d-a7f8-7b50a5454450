<?php

declare(strict_types=1);

namespace Comave\Customer\Setup\Patch\Data;

use Magento\Customer\Model\ResourceModel\Attribute;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Customer\Model\Customer;
use Magento\Customer\Setup\CustomerSetupFactory;
use Magento\Eav\Setup\EavSetupFactory;
use Magento\Eav\Model\Config as EavConfig;
use Magento\Framework\Exception\AlreadyExistsException;
use Magento\Framework\Exception\LocalizedException;
use Psr\Log\LoggerInterface;


class AddEmailInfoAttribute implements DataPatchInterface
{
    private const string CUSTOMER_EMAIL_INFO_ATTRIBUTE_CODE = 'email_info';

    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly LoggerInterface $logger,
        private readonly EavSetupFactory $eavSetupFactory,
        private readonly CustomerSetupFactory $customerSetupFactory,
        private readonly EavConfig $eavConfig,
        private readonly Attribute $attributeResource,
    ) {
    }

    /**
     * @return void
     */
    public function apply(): void
    {
        $this->moduleDataSetup->getConnection()->startSetup();

        try {
            $this->addEmailInfoCustomerAttribute();
        } catch (AlreadyExistsException $exception) {
            $this->logger->error('email_info customer attribute already exists.' . $exception->getMessage());
        } catch (\Exception $exception) {
            $this->logger->error('Error while adding email_info customer attribute' . $exception->getMessage());
        }

        $this->moduleDataSetup->getConnection()->endSetup();
    }

    /**
     * @return void
     */
    private function addEmailInfoCustomerAttribute(): void
    {
        $eavSetup = $this->eavSetupFactory->create();
        $eavSetup->addAttribute(
            Customer::ENTITY,
            'email_info',
            [
                'type' => 'varchar',
                'label' => 'Seller Email Info',
                'input' => 'text',
                'required' => 0,
                'visible' => 1,
                'user_defined' => 1,
                'sort_order' => 999,
                'position' => 999,
                'system' => 0,
                'is_used_in_grid' => true,
                'is_visible_in_grid' => true,
                'visible_on_front' => true,
            ]
        );

        $attributeSetId = $eavSetup->getDefaultAttributeSetId(Customer::ENTITY);
        $attributeGroupId = $eavSetup->getDefaultAttributeGroupId(Customer::ENTITY);

        $attribute = $this->eavConfig->getAttribute(Customer::ENTITY, 'email_info');
        $attribute->setData('attribute_set_id', $attributeSetId);
        $attribute->setData('attribute_group_id', $attributeGroupId);

        $attribute->setData('used_in_forms', [
            'adminhtml_customer',
            'customer_account_edit',
            'customer_account_create'
        ]);
        $attribute->save();

        $this->attributeResource->save($attribute);
    }

    /**
     * @inheritDoc
     */
    public function revert()
    {
        $this->moduleDataSetup->getConnection()->startSetup();

        $customerSetup = $this->customerSetupFactory->create(['setup' => $this->moduleDataSetup]);
        $customerSetup->removeAttribute(Customer::ENTITY, 'email_info');

        $this->moduleDataSetup->getConnection()->endSetup();
    }

    /**
     * @inheritDoc
     */
    public function getAliases()
    {
        return [];
    }

    /**
     * @inheritDoc
     */
    public static function getDependencies()
    {
        return [];
    }
}
