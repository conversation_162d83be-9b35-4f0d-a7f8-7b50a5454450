<?php

declare(strict_types=1);

namespace Comave\Customer\Setup\Patch\Data;

use Comave\Marketplace\Setup\Patch\Data\InstallMissingAttributesV2;
use Magento\Eav\Setup\EavSetupFactory;
use Magento\Framework\DataObject\IdentityService;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\Setup\Patch\PatchRevertableInterface;

class CustomerUuidPatch implements DataPatchInterface, PatchRevertableInterface
{
    private const CUSTOMER_UUID_ATTRIBUTE_CODE = 'commave_uuid';

    /**
     * Constructor
     *
     * @param \Magento\Framework\Setup\ModuleDataSetupInterface $moduleDataSetup
     * @param \Magento\Eav\Setup\EavSetupFactory $eavSetupFactory
     * @param \Magento\Framework\DataObject\IdentityService $identityService
     */
    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly EavSetupFactory $eavSetupFactory,
        private readonly IdentityService $identityService
    ) {
    }

    /**
     * @inheritDoc
     */
    public function apply()
    {
        $this->moduleDataSetup->startSetup();

        $this->updateCustomerAttribute();
        $this->populateExistingCustomers();

        $this->moduleDataSetup->endSetup();
    }

    /**
     * @inheritDoc
     */
    public function revert()
    {
        $this->moduleDataSetup->getConnection()->startSetup();
        $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);
        $eavSetup->removeAttribute(\Magento\Customer\Model\Customer::ENTITY, self::CUSTOMER_UUID_ATTRIBUTE_CODE);

        $this->moduleDataSetup->getConnection()->endSetup();
    }

    /**
     * @inheritDoc
     */
    public function getAliases()
    {
        return [];
    }

    /**
     * @inheritDoc
     */
    public static function getDependencies()
    {
        return [
            InstallMissingAttributesV2::class
        ];
    }

    /**
     * Create customer attribute function
     *
     * @return void
     * @phpcs:disable  SlevomatCodingStandard.Functions.FunctionLength.FunctionLength
     */
    protected function updateCustomerAttribute(): void
    {
        $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);

        $eavSetup->updateAttribute(
            \Magento\Customer\Model\Customer::ENTITY,
            self::CUSTOMER_UUID_ATTRIBUTE_CODE,
            [
                'is_unique' => true,
                'is_user_defined' => false,
                'is_visible' => false,
            ]
        );
    }

    /**
     * Create Uuid for existing customers function
     *
     * @return void
     */
    protected function populateExistingCustomers(): void
    {
        $attributeId = $this->getAttributeId(self::CUSTOMER_UUID_ATTRIBUTE_CODE);

        if (empty($attributeId)) {
            return;
        }

        $connection = $this->moduleDataSetup->getConnection();
        $customerTable = $this->moduleDataSetup->getTable('customer_entity');
        $customerVarcharTable = $this->moduleDataSetup->getTable('customer_entity_varchar');
        $customers = $connection->fetchAll(
            "SELECT c.entity_id
            FROM " . $customerTable . " AS c
            LEFT JOIN " . $customerVarcharTable . " AS cv
            ON c.entity_id = cv.entity_id AND cv.attribute_id = :attributeId
            WHERE cv.value IS NULL",
            ['attributeId' => $attributeId]
        );

        $data = [];

        foreach ($customers as $customer) {
            $uuid = $this->identityService->generateId();
            $data[] = [
                'attribute_id' => $attributeId,
                'entity_id' => $customer['entity_id'],
                'value' => $uuid
            ];
        }

        if (!empty($data)) {
            $connection->insertOnDuplicate($customerVarcharTable, $data, ['value']);
        }
    }

    /**
     * Get Attribute Id function
     *
     * @param string $attributeCode
     * @return mixed
     */
    protected function getAttributeId(string $attributeCode): mixed
    {
        $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);

        return $eavSetup->getAttributeId(\Magento\Customer\Model\Customer::ENTITY, $attributeCode);
    }
}
