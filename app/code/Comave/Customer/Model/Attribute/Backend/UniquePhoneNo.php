<?php

declare(strict_types=1);

namespace Comave\Customer\Model\Attribute\Backend;

use Magento\Eav\Model\Entity\Attribute\Backend\AbstractBackend;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Framework\Exception\LocalizedException;

/**
 * Backend model to enforce uniqueness of the 'phone_no' attribute for customer entities.
 */
class UniquePhoneNo extends AbstractBackend
{
    /**
     * @var CustomerRepositoryInterface
     */
    private CustomerRepositoryInterface $customerRepository;

    /**
     * @var SearchCriteriaBuilder
     */
    private SearchCriteriaBuilder $criteriaBuilder;

    /**
     * Constructor.
     *
     * @param CustomerRepositoryInterface $customerRepository  Repository to access customer data
     * @param SearchCriteriaBuilder       $criteriaBuilder     Builder for constructing search criteria
     */
    public function __construct(
        CustomerRepositoryInterface $customerRepository,
        SearchCriteriaBuilder $criteriaBuilder
    ) {
        $this->customerRepository = $customerRepository;
        $this->criteriaBuilder = $criteriaBuilder;
    }

    /**
     * Executes before the customer entity is saved.
     *
     * Validates that the phone number is unique among all customers.
     *
     * @param CustomerInterface|\Magento\Framework\DataObject $customer
     * @return AbstractBackend
     * @throws LocalizedException If the phone number is already used by another customer
     */
    public function beforeSave($customer): AbstractBackend
    {
        $attributeCode = $this->getAttribute()->getAttributeCode();
        $phone = $customer->getData($attributeCode);

        if (!$phone) {
            return parent::beforeSave($customer);
        }

        $criteria = $this->criteriaBuilder
            ->addFilter($attributeCode, $phone)
            ->addFilter('entity_id', $customer->getId() ?: 0, 'neq')
            ->create();

        $existingCustomers = $this->customerRepository->getList($criteria);

        if ($existingCustomers->getTotalCount() > 0) {
            throw new LocalizedException(
                __("A customer with the phone number '%1' already exists.", $phone)
            );
        }

        $customer->setData($attributeCode, $phone);

        return parent::beforeSave($customer);
    }
}
