<?php

declare(strict_types=1);

namespace Comave\DbClean\Setup\Patch\Schema;

use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Setup\Patch\SchemaPatchInterface;

class RemoveTables implements SchemaPatchInterface
{
    /**
     * @param ResourceConnection $resourceConnection
     */
    public function __construct(private readonly ResourceConnection $resourceConnection)
    {
    }

    /**
     * @return array|string[]
     */
    public static function getDependencies(): array
    {
        return [];
    }

    /**
     * @return array|string[]
     */
    public function getAliases(): array
    {
        return [];
    }

    /**
     * @return $this
     * @throws \Exception
     */
    public function apply(): self
    {
        $connection = $this->resourceConnection->getConnection('write');
        $tablesToDelete = [
            'coditron_lixtask',
            'coditron_scrachcard',
            'coditron_spinwheel',
            'coditron_predict',
            'quiz_answers',
            'quiz_questions',
            'quiz_task',
        ];

        foreach ($tablesToDelete as $table) {
            if (!$connection->isTableExists($connection->getTableName($table))) {
                continue;
            }

            echo "Deleting table '{$table}'" . PHP_EOL;
            $query = <<<DELETEQUERY
    DROP TABLE IF EXISTS {$table}
    DELETEQUERY;
            $connection->query($query);
        }

        return $this;
    }
}
