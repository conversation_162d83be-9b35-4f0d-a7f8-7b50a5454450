<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_Marketplace
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */

namespace Comave\Sportsclub\Block\Account\Dashboard;

use Magento\Framework\App\ObjectManager;
use Magento\Framework\View\Element\Template\Context;
use Magento\Sales\Model\ResourceModel\Order\CollectionFactory as OrderCollectionFactory;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Model\CustomerFactory;
use Magento\Sales\Model\OrderFactory;

class RevenueChart extends \Magento\Framework\View\Element\Template
{
    /**
     * GOOGLE_API_URL Google Api URL.
     */
    public const GOOGLE_API_URL = 'http://chart.apis.google.com/chart';

    /**
     * Seller statistics graph width.
     *
     * @var string
     */
    protected $_width = '350';

    /**
     * Seller statistics graph height.
     *
     * @var string
     */
    protected $_height = '169';

    /**
     * @var \Magento\Customer\Model\Session
     */
    protected $_customerSession;

    /**
     * @var \Webkul\Marketplace\Block\Account\Dashboard
     */
    protected $dashboard;

    /**
     * @var \Webkul\Marketplace\Helper\Dashboard\Data
     */
    protected $dashboardHelper;

    protected $orderCollectionFactory;

    protected $customerFactory;

    protected $customerRepositoryInterface;

    protected $orderFactory;

    protected $customer;

    /**
     * Construct
     *
     * @param \Magento\Customer\Model\Session $customerSession
     * @param \Magento\Framework\View\Element\Template\Context $context
     * @param \Magento\Sales\Model\ResourceModel\Order\CollectionFactory $orderCollectionFactory
     * @param \Magento\Customer\Api\CustomerRepositoryInterface $customerRepositoryInterface
     * @param \Magento\Customer\Model\CustomerFactory $customerFactory
     * @param \Magento\Sales\Model\OrderFactory $orderFactory
     * @param \Webkul\Marketplace\Block\Account\Dashboard $dashboard
     * @param \Webkul\Marketplace\Helper\Dashboard\Data $dashboardHelper
     * @param array $data
     */
    public function __construct(
        \Magento\Customer\Model\Session $customerSession,
        Context $context,
        OrderCollectionFactory $orderCollectionFactory,
        CustomerRepositoryInterface $customerRepositoryInterface,
        CustomerFactory $customerFactory,
        OrderFactory $orderFactory,
        \Webkul\Marketplace\Block\Account\Dashboard $dashboard = null,
        \Webkul\Marketplace\Helper\Dashboard\Data $dashboardHelper = null,
        array $data = []
    ) {
        $this->_customerSession = $customerSession;
        $this->orderCollectionFactory = $orderCollectionFactory;
        $this->customer = $customerFactory;
        $this->orderFactory = $orderFactory;
        $this->dashboard = $dashboard ?: ObjectManager::getInstance()
            ->create(\Webkul\Marketplace\Block\Account\Dashboard::class);
        $this->dashboardHelper = $dashboardHelper ?: ObjectManager::getInstance()
            ->create(\Webkul\Marketplace\Helper\Dashboard\Data::class);
        parent::__construct($context, $data);
    }

    /**
     * Get seller statistics graph image url.
     *
     * @return string
     */
    public function getSellerStatisticsGraphUrl()
    {
        $params = [
            'cht' => 'p',
        ];
        $getTopSaleCategories = $this->dashboard->getTopSaleCategories();
        $params['chl'] = implode('|', $getTopSaleCategories['category_arr']);
        $chcoArr = [];
        $catCount = count($getTopSaleCategories['category_arr']);
        for ($i = 1; $i <= $catCount; ++$i) {
            array_push($chcoArr, $this->randString());
        }

        $params['chco'] = implode('|', $chcoArr);
        $params['chd'] = 't:' . implode(',', $getTopSaleCategories['percentage_arr']);
        $params['chdl'] = implode('%|', $getTopSaleCategories['percentage_arr']);
        $params['chdl'] = $params['chdl'] . '%';

        $valueBuffer = [];

        // seller statistics graph size
        $params['chs'] = $this->_width . 'x' . $this->_height;

        // return the encoded graph image url
        $_sellerDashboardHelperData = $this->dashboardHelper;
        $getParamData = urlencode(base64_encode(json_encode($params)));
        $getEncryptedHashData = $_sellerDashboardHelperData->getChartEncryptedHashData($getParamData);
        $params = [
            'param_data' => $getParamData,
            'encrypted_data' => $getEncryptedHashData,
        ];

        return $this->getUrl(
            '*/*/dashboard_tunnel',
            ['_query' => $params, '_secure' => $this->getRequest()->isSecure()]
        );
    }

    /**
     * Get random string
     *
     * @param string $charset
     * @return string
     */
    public function randString(
        $charset = 'ABC0123456789'
    ) {
        $length = 6;
        $str = '';
        $count = strlen($charset);
        while ($length--) {
            $str .= $charset[random_int(0, $count - 1)];
        }

        return $str;
    }

    public function getCustomer()
    {
        return $this->customer;
    }

    /**
     * Get customerid
     *
     * @return int
     */
    public function getCustomerId()
    {
        return $this->_customerSession->getCustomerId();
    }

    public function getCustClubIdentifier()
    {
        $clubIdentifier = false;

        $customerId = $this->getCustomerId();

        if ($customerId) {
            try {
                $currentCustomer = $this->customer->create()->load($customerId);
                $clubIdentifier = $currentCustomer->getWkv_club_unique_identfier();
            } catch (\Exception $e) {
                $this->_logger->error($e->getMessage());
            }
        } else {
            $this->_logger->error("No customer ID provided.");
        }

        return $clubIdentifier;
    }
    public function getComAveRevenue()
    {
        try {
            $clubIdentifier = $this->getCustClubIdentifier();
            $collection = $this->orderCollectionFactory->create();
            $collection->addFieldToFilter('club_name', $clubIdentifier);
            $totalAmount = 0.00;
            foreach ($collection as $item) {
                $totalAmount += $item->getClubCommission(); // Assuming club_commission is a valid attribute
            }
            return $totalAmount;
        } catch (\Exception $e) {
            $this->_logger->error($e->getMessage());
            return 0.00; // Return 0 in case of any exception
        }
    }

    public function getTamiasRevenue()
    {
        // Replace this placeholder value with your actual method implementation
        return 250;
    }

    public function getRevenueData()
    {
        // Get revenue for comave store
        $comaveRevenue = $this->getComAveRevenue();

        // Calculate total revenue
        $totalRevenue = $comaveRevenue;

        // Calculate percentage for each store
        $comavePercentage = $totalRevenue > 0 ? ($comaveRevenue / $totalRevenue) * 100 : 0;

        // Format percentages to two decimal places
        $comavePercentage = number_format($comavePercentage, 2);

        // Prepare data
        $revenueData = [
            'Comave' => $comavePercentage,
        ];

        return $revenueData;
    }


}
