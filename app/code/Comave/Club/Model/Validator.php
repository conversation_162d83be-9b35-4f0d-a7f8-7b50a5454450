<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Club\Model;

use Comave\Club\Exception\NotUniquePropertyException;
use Comave\Club\Model\ResourceModel\Club\Collection;
use Comave\Club\Model\ResourceModel\Club\CollectionFactory;

class Validator
{
    /**
     * @param \Comave\Club\Model\ResourceModel\Club\CollectionFactory $clubCollectionFactory
     */
    public function __construct(private readonly CollectionFactory $clubCollectionFactory)
    {
    }

    /**
     * @param \Comave\Club\Model\Club $model
     * @return void
     * @throws \Comave\Club\Exception\NotUniquePropertyException
     */
    public function validate(Club $model): void
    {
        if ($model->hasDataChanges()) {
            $this->validateUniqueness($model);
        }
    }

    /**
     * @param \Comave\Club\Model\Club $model
     * @return void
     * @throws \Comave\Club\Exception\NotUniquePropertyException
     */
    private function validateUniqueness(Club $model): void
    {
        $fields = [
            'name' => 'Club Name',
            'uniqueid' => 'Club Unique Identifier',
            'url_key' => 'URL Key',
        ];
        foreach ($fields as $field => $label) {
            if ($model->getOrigData($field) !== $model->getData($field)) {
                $this->validateField($field, $label, $model->getData($field));
            }
        }
    }

    /**
     * @throws \Comave\Club\Exception\NotUniquePropertyException
     */
    private function validateField(string $field, string $label, string $value): void
    {
        $messageFormat = 'Could not save the club. "%1" with value "%2" is already in use.';
        if ($this->clubExists($field, $value)) {
            throw new NotUniquePropertyException(__($messageFormat, $label, $value));
        }
    }

    /**
     * @param string $field
     * @param string $value
     * @return bool
     */
    private function clubExists(string $field, string $value): bool
    {
        /** @var Collection $collection */
        $collection = $this->clubCollectionFactory->create();
        $collection->addFieldToFilter($field, $value);

        return $collection->getSize() > 0;
    }
}