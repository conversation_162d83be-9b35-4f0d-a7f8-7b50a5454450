<?php
/**
 * Copyright © Commercial Avenue
 */

namespace Comave\Club\Controller\Adminhtml\Club;

use Comave\Club\Api\ClubRepositoryInterface;
use Comave\Club\Exception\NotUniquePropertyException;
use Comave\Club\Model\ClubFactory;
use Comave\Club\Model\Validator;
use Exception;
use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Backend\Helper\Js;
use Magento\Backend\Model\View\Result\Redirect;
use Magento\Catalog\Model\Product\Url;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\File\UploaderFactory;
use Magento\Framework\Filesystem;
use Psr\Log\LoggerInterface;
use RuntimeException;

class Save extends Action
{
    /**
     * @param \Magento\Backend\App\Action\Context $context
     * @param \Comave\Club\Model\Validator $validator
     * @param \Magento\Framework\Filesystem $fileSystem
     * @param \Magento\Backend\Helper\Js $jsHelper
     * @param \Psr\Log\LoggerInterface $logger
     * @param \Magento\Catalog\Model\Product\Url $productUrl
     * @param \Comave\Club\Api\ClubRepositoryInterface $clubRepository
     * @param \Magento\Framework\File\UploaderFactory $uploaderFactory
     * @param \Comave\Club\Model\ClubFactory $clubFactory
     */
    public function __construct(
        Context $context,
        private readonly Validator $validator,
        private readonly Filesystem $fileSystem,
        private readonly Js $jsHelper,
        private readonly LoggerInterface $logger,
        private readonly Url $productUrl,
        private readonly ClubRepositoryInterface $clubRepository,
        private readonly UploaderFactory $uploaderFactory,
        private readonly ClubFactory $clubFactory
    ) {
        parent::__construct($context);
    }

    /**
     * {@inheritdoc}
     */
    protected function _isAllowed(): bool
    {
        return $this->_authorization->isAllowed('Comave_Club::club_save');
    }

    /**
     * Save action
     *
     * @return \Magento\Framework\Controller\ResultInterface
     * @throws \Magento\Framework\Exception\ValidatorException
     */
    public function execute(): ResultInterface
    {
        $data = $this->getRequest()->getPostValue();
        /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultRedirectFactory->create();
        $isNew = false;

        if ($data) {
            $id = $this->getRequest()->getParam('club_id');
            $clubImage = $clubLogo = $clubBanner = $clubWatermarkImage = '';
            if ($id) {
                $model = $this->clubRepository->get($id);
                $clubImage = $model->getImage();
                $clubLogo = $model->getClogo();
                $clubBanner = $model->getClubBanner();
                $clubWatermarkImage = $model->getClubWatermarkImage();
            } else {
                $model = $this->clubFactory->create();
                $isNew = true;
            }

            /** @var \Magento\Framework\Filesystem\Directory\Read $mediaDirectory */
            $mediaDirectory = $this->fileSystem->getDirectoryRead(DirectoryList::MEDIA);

            $imagePath = '';
            if (!$isNew) {
                $imagePath = $mediaDirectory->getAbsolutePath($model->getImage());
            }

            if (isset($data['image']['delete']) && file_exists($imagePath)) {
                unlink($imagePath);
                $data['image'] = '';
                if ($clubImage && $clubLogo && $clubImage == $clubLogo) {
                    $data['clogo'] = '';
                }
            }
            if (isset($data['image']) && is_array($data['image'])) {
                unset($data['image']);
            }

            if ($image = $this->uploadImage('image')) {
                $data['image'] = $image;
            }

            // Delete, Upload club logo
            $clubLogoPath = '';
            if (!$isNew) {
                $clubLogoPath = $mediaDirectory->getAbsolutePath($model->getClogo());
            }
            if (isset($data['clogo']['delete']) && file_exists($clubLogoPath)) {
                unlink($clubLogoPath);
                $data['clogo'] = '';
                if ($clubImage && $clubLogo && $clubImage == $clubLogo) {
                    $data['image'] = '';
                }
            }
            if (isset($data['clogo']) && is_array($data['clogo'])) {
                unset($data['clogo']);
            }
            if ($clogo = $this->uploadImage('clogo')) {
                $data['clogo'] = $clogo;
            }

            // Delete, Upload club banner
            $clubBannerPath = '';
            if (!$isNew) {
                $clubBannerPath = $mediaDirectory->getAbsolutePath($model->getClubBanner());
            }
            if (isset($data['club_banner']['delete']) && file_exists($clubBannerPath)) {
                unlink($clubBannerPath);
                $data['club_banner'] = '';
                if ($clubBanner && $clubWatermarkImage && $clubBanner == $clubWatermarkImage) {
                    $data['club_watermark_image'] = '';
                }
            }

            if (isset($data['club_banner']) && is_array($data['club_banner'])) {
                unset($data['club_banner']);
            }

            if ($clubBanner = $this->uploadImage('club_banner')) {
                $data['club_banner'] = $clubBanner;
            }

            // Delete, Upload club watermark image
            $clubWatermarkImagePath = '';
            if (!$isNew) {
                $clubWatermarkImagePath = $mediaDirectory->getAbsolutePath($model->getClubWatermarkImage());

            }
            if (isset($data['club_watermark_image']['delete']) && file_exists($clubWatermarkImagePath)) {
                unlink($clubWatermarkImagePath);
                $data['club_watermark_image'] = '';
                if ($clubBanner && $clubWatermarkImage && $clubBanner == $clubWatermarkImage) {
                    $data['club_banner'] = '';
                }
            }

            if (isset($data['club_watermark_image']) && is_array($data['club_watermark_image'])) {
                unset($data['club_watermark_image']);
            }

            if ($clubWatermarkImage = $this->uploadImage('club_watermark_image')) {
                $data['club_watermark_image'] = $clubWatermarkImage;
            }

            if ($data['url_key'] == '') {
                $data['url_key'] = $data['name'];
            }
            $urlKey = $this->productUrl->formatUrlKey($data['url_key']);
            $data['url_key'] = $urlKey;

            $links = $this->getRequest()->getPost('links');
            $links = is_array($links) ? $links : [];
            if (!empty($links) && isset($links['related'])) {
                $products = $this->jsHelper->decodeGridSerializedInput($links['related']);
                $data['products'] = $products;
            }

            try {
                $model->setData($data);
                $this->validator->validate($model);
                $this->clubRepository->save($model);
                $this->messageManager->addSuccessMessage(__('You saved this club.'));
                $this->_getSession()->setFormData(false);
                if ($this->getRequest()->getParam('back')) {
                    return $resultRedirect->setPath(
                        '*/*/edit',
                        ['club_id' => $model->getId(), '_current' => true]
                    );
                }

                return $resultRedirect->setPath('*/*/');
            } catch (NotUniquePropertyException|LocalizedException|RuntimeException $e) {
                $this->messageManager->addErrorMessage($e->getMessage());
            } catch (Exception $e) {
                $this->messageManager->addErrorMessage(
                    $e,
                    __('Something went wrong while saving the club.')
                );
            }

            $this->_getSession()->setFormData($data);

            return $resultRedirect->setPath(
                '*/*/edit',
                [
                    'club_id' => $this->getRequest()->getParam('club_id'),
                ]
            );
        }

        return $resultRedirect->setPath('*/*/');
    }

    /**
     * @param string $fieldId
     * @return \Magento\Backend\Model\View\Result\Redirect|string
     */
    public function uploadImage(string $fieldId = 'image'): Redirect|string
    {
        /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultRedirectFactory->create();

        $files = $this->_request->getFiles($fieldId);
        if (!empty($files) && $files['name'] != '') {
            $uploader = $this->uploaderFactory->create(['fileId' => $fieldId]);

            /** @var \Magento\Framework\Filesystem\Directory\Read $mediaDirectory */
            $mediaDirectory = $this->fileSystem->getDirectoryRead(DirectoryList::MEDIA);
            $mediaFolder = 'comave/club/';
            try {
                $uploader->setAllowedExtensions(
                    array(
                        'jpeg',
                        'jpg',
                        'png',
                        'gif',
                        'JPEG',
                        'JPG',
                        'PNG',
                        'GIF',
                        'webp',
                        'WEBP',
                        'svg',
                        'SVG',
                        'avif',
                        'AVIF',
                        'jfif',
                        'JFIF',
                    )
                );
                $uploader->setAllowRenameFiles(true);
                $uploader->setFilesDispersion(false);
                $result = $uploader->save($mediaDirectory->getAbsolutePath($mediaFolder));

                return $mediaFolder.$result['name'];
            } catch (Exception $e) {
                $this->logger->critical($e);
                $this->messageManager->addErrorMessage(
                    $e->getMessage()
                );

                return $resultRedirect->setPath(
                    '*/*/edit',
                    [
                        'club_id' => $this->getRequest()->getParam('club_id'),
                    ]
                );
            }
        }

        return '';
    }
}
