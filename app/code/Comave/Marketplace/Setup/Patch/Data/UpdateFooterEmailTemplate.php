<?php

declare(strict_types=1);

namespace Comave\Marketplace\Setup\Patch\Data;

use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Module\Dir;
use Magento\Framework\Module\Dir\Reader as DirReader;
use Magento\Framework\Filesystem\Io\File as IoFile;
use Magento\Framework\App\DeploymentConfig;
use Psr\Log\LoggerInterface;

/**
 * Patch class to update the footer email template with dynamic frontend base URL.
 */
class UpdateFooterEmailTemplate implements DataPatchInterface
{
    /**
     * The file name of the email footer template to load and inject.
     */
    private const EMAIL_TEMPLATE_FILE = 'email_footer_template.html';

    /**
     * The original template code used to identify the correct email template to update.
     */
    private const ORIG_TEMPLATE_CODE = 'design_email_footer_template';

    /**
     * Directory inside the module where the source HTML template is located.
     */
    private const DEFAULT_SOURCE_DIR = 'install-data';

    /**
     * @param ModuleDataSetupInterface $moduleDataSetup
     * @param DirReader $dirReader
     * @param IoFile $ioFile
     * @param DeploymentConfig $deploymentConfig
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly DirReader $dirReader,
        private readonly IoFile $ioFile,
        private readonly DeploymentConfig $deploymentConfig,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Apply the patch: load the HTML template, inject the frontend URL,
     * and update the designated email template in the database.
     *
     * @return void
     */
    public function apply(): void
    {
        $this->moduleDataSetup->getConnection()->startSetup();

        $htmlContent = $this->getFileContent(self::EMAIL_TEMPLATE_FILE);
        if (!$htmlContent) {
            return;
        }

        $frontendBaseUrl = $this->deploymentConfig->get('frontend_base_url') ?? '';
        $htmlContent = str_replace('{{frontend_base_url}}', rtrim($frontendBaseUrl, '/'), $htmlContent);

        try {
            $this->moduleDataSetup->getConnection()->update(
                $this->moduleDataSetup->getTable('email_template'),
                ['template_text' => $htmlContent],
                ['orig_template_code = ?' => self::ORIG_TEMPLATE_CODE]
            );
        } catch (CouldNotSaveException $e) {
            $this->logger->error('Failed to update the email template: ' . $e->getMessage());
        }

        $this->moduleDataSetup->getConnection()->endSetup();
    }

    /**
     * Get the full path to the directory containing email template source files.
     *
     * @return string
     */
    private function getSourceDir(): string
    {
        return $this->dirReader->getModuleDir(Dir::MODULE_ETC_DIR, 'Comave_Marketplace')
            . DIRECTORY_SEPARATOR . self::DEFAULT_SOURCE_DIR . DIRECTORY_SEPARATOR;
    }

    /**
     * Load the contents of the given template file.
     *
     * @param string $file
     * @return string
     */
    private function getFileContent(string $file): string
    {
        try {
            return $this->ioFile->read($this->getSourceDir() . $file);
        } catch (\Exception $e) {
            $this->logger->error(sprintf('Template file "%s" does not exist. Error: %s', $file, $e->getMessage()));
            return '';
        }
    }

    /**
     * @inheritdoc
     */
    public static function getDependencies(): array
    {
        return [];
    }

    /**
     * @inheritdoc
     */
    public function getAliases(): array
    {
        return [];
    }
}
