<?php

declare(strict_types=1);

namespace Comave\Marketplace\Plugin;

use Magento\Catalog\Model\Product\Attribute\OptionManagement;
use Magento\Eav\Model\Config as EavConfig;
use Magento\Catalog\Api\Data\ProductAttributeInterface;
use Magento\Eav\Api\Data\AttributeOptionInterface;
use Magento\Eav\Model\Entity\Attribute\AbstractAttribute;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Webapi\Exception as WebapiException;

class RestrictAddAttributeOption
{
    public const STATUS_DISABLED = 0;
    public const STATUS_ENABLED = 1;
    public const ATTRIBUTE_IS_SELLER_EDITABLE = 'is_seller_editable';

    public function __construct(
        private readonly EavConfig $eavConfig,
    ) {}

    /**
     * @param OptionManagement $subject
     * @param string $attributeCode
     * @param AttributeOptionInterface $option
     * @return array
     * @throws LocalizedException
     */
    public function beforeAdd(OptionManagement $subject, string $attributeCode, AttributeOptionInterface $option): array
    {
        $attribute = $this->eavConfig->getAttribute(
            ProductAttributeInterface::ENTITY_TYPE_CODE,
            $attributeCode
        );

        if (!$attribute || !$attribute->getId()) {
            throw new WebapiException(
                __('Unable to update attribute "%1"', $attributeCode),
                0,
                WebapiException::HTTP_NOT_FOUND
            );
        }

        if ((int) $attribute->getIsSellerEditable() === self::STATUS_DISABLED) {
            throw new WebapiException(
                __('You are not allowed to add options to the "%1" attribute.', $attributeCode),
                0,
                WebapiException::HTTP_UNAUTHORIZED
            );
        }

        return [$attributeCode, $option];
    }

    /**
     * Check if the attribute is editable by seller
     *
     * @param AbstractAttribute|ProductAttributeInterface $attribute
     * @return bool
     */
    public static function isEditable($attribute): bool
    {
        if (!$attribute instanceof AbstractAttribute) {
            return false;
        }
        return (int) $attribute->getData(self::ATTRIBUTE_IS_SELLER_EDITABLE) !== self::STATUS_DISABLED;
    }
}
