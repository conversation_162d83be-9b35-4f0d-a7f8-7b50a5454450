<?php

declare(strict_types=1);

namespace Comave\Marketplace\Block\Adminhtml\Product\Attribute\Edit;

class Tabs extends \Magento\Catalog\Block\Adminhtml\Product\Attribute\Edit\Tabs
{
    /**
     * @return Tabs
     */
    protected function _beforeToHtml(): Tabs
    {
        $this->addTabAfter(
            'marketplace',
            [
                'label' => __('Marketplace'),
                'title' => __('Marketplace'),
                'content' => $this->getChildHtml('marketplace')
            ],
            'front'
        );

        return parent::_beforeToHtml();
    }
}
