<?php

declare(strict_types=1);

namespace Comave\Marketplace\Block\Adminhtml\Product\Attribute\Edit\Tab;

use Magento\Backend\Block\Widget\Form\Generic;
use Magento\Backend\Block\Template\Context;
use Magento\Catalog\Model\Entity\Attribute;
use Magento\Framework\Registry;
use Magento\Framework\Data\FormFactory;
use Magento\Eav\Block\Adminhtml\Attribute\PropertyLocker;
use Magento\Config\Model\Config\Source\Yesno;

class Marketplace extends Generic
{
    public function __construct(
        private readonly Context $context,
        private readonly Registry $registry,
        private readonly FormFactory $formFactory,
        private readonly Yesno $yesNo,
        private readonly PropertyLocker $propertyLocker,
        private readonly array $data = []
    ) {
        parent::__construct($context, $registry, $formFactory, $data);
    }

    protected function _prepareForm()
    {
        /** @var Attribute $attributeObject */
        $attributeObject = $this->_coreRegistry->registry('entity_attribute');
        /** @var \Magento\Framework\Data\Form $form */
        $form = $this->formFactory->create(
            ['data' => ['id' => 'edit_form', 'action' => $this->getData('action'), 'method' => 'post']]
        );

        $fieldset = $form->addFieldset(
            'front_fieldset',
            ['legend' => __('Attribute Properties')]
        );

        $yesnoSource = $this->yesNo->toOptionArray();
        $fieldset->addField(
            'is_seller_editable',
            'select',
            [
                'name' => 'is_seller_editable',
                'label' => __('Seller can update?'),
                'title' => __('Seller can update?'),
                'values' => $yesnoSource
            ]
        );
        $this->setForm($form);
        $form->setValues($attributeObject->getData());
        $this->propertyLocker->lock($form);
        return parent::_prepareForm();
    }
}
