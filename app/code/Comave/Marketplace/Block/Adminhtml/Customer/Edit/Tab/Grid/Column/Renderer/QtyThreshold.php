<?php
declare(strict_types=1);

namespace Comave\Marketplace\Block\Adminhtml\Customer\Edit\Tab\Grid\Column\Renderer;

use Comave\Marketplace\Model\Service\SellerProductsStock;
use Magento\Framework\DataObject;

class QtyThreshold extends \Magento\Backend\Block\Widget\Grid\Column\Renderer\AbstractRenderer
{
    /**
     * @param SellerProductsStock $sellerProductsStock
     */
    public function __construct(
        private SellerProductsStock $sellerProductsStock,
    ) {
    }

    /**
     * @param DataObject $row
     * @return \Magento\Framework\Phrase
     */
    public function render(DataObject $row): \Magento\Framework\Phrase
    {
        $sellerThreshold = $this->sellerProductsStock->getSellerLowStockTreshold($row['entity_id']);
        return $this->sellerProductsStock->getStockStatus((int)$row['qty'], $sellerThreshold);
    }
}
