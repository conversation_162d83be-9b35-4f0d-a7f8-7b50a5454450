<?php
namespace Comave\Marketplace\Controller\Account;

use Magento\Customer\Model\Session;
use Magento\Framework\App\Action\Context;
use Magento\Framework\Data\Form\FormKey\Validator as FormKeyValidator;
use Magento\Framework\App\RequestInterface;
use Magento\Customer\Model\Url as CustomerUrl;

class SaveLowStockThreshold extends \Magento\Customer\Controller\AbstractAccount
{

    /**
     * @param Context $context
     * @param Session $customerSession
     * @param FormKeyValidator $formKeyValidator
     * @param CustomerUrl $customerUrl
     */
    public function __construct(
        Context $context,
        protected   Session $customerSession,
        protected   FormKeyValidator $formKeyValidator,
        protected   CustomerUrl $customerUrl
    ) {
        parent::__construct(
            $context
        );
    }

    /**
     * @param RequestInterface $request
     * @return \Magento\Framework\App\ResponseInterface
     * @throws \Magento\Framework\Exception\NotFoundException
     * @throws \Magento\Framework\Exception\SessionException
     */
    public function dispatch(RequestInterface $request)
    {
        $loginUrl = $this->customerUrl->getLoginUrl();

        if (!$this->customerSession->authenticate($loginUrl)) {
            $this->_actionFlag->set('', self::FLAG_NO_DISPATCH, true);
        }
        return parent::dispatch($request);
    }

    /**
     * Seller's SavePaymentInfo action.
     *
     * @return \Magento\Framework\Controller\Result\RedirectFactory
     */
    public function execute()
    {
        if ($this->getRequest()->isPost()) {
            try {
                if (!$this->formKeyValidator->validate($this->getRequest())) {
                    return $this->resultRedirectFactory->create()->setPath(
                        '*/*/editProfile',
                        ['_secure' => $this->getRequest()->isSecure()]
                    );
                }
                $fields = $this->getRequest()->getParams();
                $customer = $this->customerSession->getCustomer();
                $customer->setSellerLowStockThresholdGrid($fields['seller_low_stock_threshold']);;
                $customer->save();
                $this->messageManager->addSuccess(
                    __('Low Stock Threshold has been updated successfully.')
                );
            } catch (\Exception $e) {
                $this->messageManager->addError($e->getMessage());
            }
        }
        return $this->resultRedirectFactory->create()->setPath(
            '*/*/editProfile',
            ['_secure' => $this->getRequest()->isSecure()]
        );
    }
}
