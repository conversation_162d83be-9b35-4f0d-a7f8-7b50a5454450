<?php

namespace Comave\Marketplace\Controller\Adminhtml\Seller;

use Magento\Customer\Controller\RegistryConstants;

class Product extends \Magento\Backend\App\Action
{

    /**
     * @param \Magento\Backend\App\Action\Context               $context
     * @param \Magento\Framework\Controller\Result\RawFactory   $resultRawFactory
     * @param \Magento\Framework\View\LayoutFactory             $layoutFactory
     * @param \Magento\Framework\Registry                       $coreRegistry
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        private \Magento\Framework\Controller\Result\RawFactory $resultRawFactory,
        private \Magento\Framework\View\LayoutFactory $layoutFactory,
        private \Magento\Framework\Registry $coreRegistry
    ) {
        parent::__construct($context);
    }

    /**
     * Grid Action Display list of products related to current customer.
     *
     * @return \Magento\Framework\Controller\Result\Raw
     */
    public function execute()
    {
        $customer = $this->initCurrentCustomer(true);
        if (!$customer) {
            /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
            $resultRedirect = $this->resultRedirectFactory->create();
            return $resultRedirect->setPath('customer/index/index', ['_current' => true]);
        }
        /** @var \Magento\Framework\Controller\Result\Raw $resultRaw */
        $resultRaw = $this->resultRawFactory->create();
        return $resultRaw->setContents(
            $this->layoutFactory->create()->createBlock(
                \Comave\Marketplace\Block\Adminhtml\Customer\Edit\Tab\Grid\Product::class,
                'seller.product.grid'
            )->toHtml()
        );
    }

    /**
     * Customer initialization.
     *
     * @return string customer id
     */
    protected function initCurrentCustomer(): string
    {
        $customerId = (int)$this->getRequest()->getParam('id');

        if ($customerId) {
            $this->coreRegistry->register(RegistryConstants::CURRENT_CUSTOMER_ID, $customerId);
        }

        return $customerId;
    }

    /**
     * Check for is allowed
     *
     * @return boolean
     */
    protected function _isAllowed(): bool
    {
        return $this->_authorization->isAllowed('Webkul_Marketplace::seller');
    }
}
