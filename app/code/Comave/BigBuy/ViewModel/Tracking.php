<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\BigBuy\ViewModel;

use Comave\Sales\Service\Order\TrackingService;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Sales\Api\Data\OrderInterface;

class Tracking implements ArgumentInterface
{
    /**
     * @var array|null
     */
    private ?array $trackingNumbers = null;

    /**
     * @param \Comave\Sales\Service\Order\TrackingService $trackingService
     */
    public function __construct(
        private readonly TrackingService $trackingService
    ) {
    }

    /**
     * @return array
     */
    public function getTrackingNumbers(): array
    {
        return $this->trackingNumbers ?: [];
    }

    /**
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @return bool
     */
    public function hasTrackingNumbers(OrderInterface $order): bool
    {
        if (is_null($this->trackingNumbers)) {
            $trackings = $this->trackingService->fetch($order);
            $this->trackingNumbers = $this->formatTrackingData($trackings);
        }

        return (bool)count($this->trackingNumbers);
    }

    /**
     * @param array $trackings
     * @return array
     */
    private function formatTrackingData(array $trackings): array
    {
        if (empty($trackings)) {
            return [];
        }
        $trackingNumbers = [];
        foreach ($trackings as $trackingItem) {
            $tracking = [];
            $tracking['number'] = $trackingItem->getTrackingNumber();
            $tracking['status'] = $trackingItem->getTrackingStatus();
            $tracking['date'] = date('l, Y-m-d H:m:s', strtotime($trackingItem->getDate()));
            $trackingNumbers[] = $tracking;
        }

        return $trackingNumbers;
    }
}
