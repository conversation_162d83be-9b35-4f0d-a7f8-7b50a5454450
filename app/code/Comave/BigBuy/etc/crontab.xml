<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Cron:etc/crontab.xsd">
    <group id="comave_group">
        <job name="comave_bigbuy_brand_collect" instance="Comave\BigBuy\Cron\BrandCollector" method="execute">
            <schedule>0 0 30 2 *</schedule> <!-- 0 0 1 * * --> <!-- At 00:00 on day-of-month 1 -->
        </job>
        <job name="comave_bigbuy_order_tracking_collect" instance="Comave\BigBuy\Cron\OrderTrackingCollector" method="execute">
            <schedule>*/2 * * * *</schedule> <!-- At every 2nd minute -->
        </job>
        <job name="comave_bigbuy_category_collect" instance="Comave\BigBuy\Cron\CategoryCollector" method="execute">
            <schedule>0 0 30 2 *</schedule> <!-- 0 0 1 * * --> <!-- At 00:00 on day-of-month 1 -->
        </job>
        <job name="comave_bigbuy_async_place_order" instance="Comave\BigBuy\Cron\AsyncPlaceOrder" method="execute">
            <schedule>0 0 30 2 *</schedule> <!-- 0 0 1 * * --> <!-- At 00:00 on day-of-month 1 -->
        </job>
        <job name="comave_bigbuy_order_status_sync" instance="Comave\BigBuy\Cron\OrderStatusSync" method="execute">
            <schedule>0 0/12 * * *</schedule> <!-- on [00:00:00, 12:00:00] every day  -->
        </job>
    </group>
</config>