<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\BigBuy\Console\Command;

use Comave\BigBuy\Service\Order\Tracking\Collector;
use Exception;
use Magento\Framework\App\Area;
use Magento\Framework\App\State as State;
use Magento\Framework\Console\Cli;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Registry;
use Magento\Store\Model\Store;
use Magento\Store\Model\StoreManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBarFactory;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class GetOrderTrackingList extends Command
{
    /**
     * @param \Magento\Framework\Registry $registry
     * @param \Magento\Framework\App\State $appState
     * @param \Magento\Store\Model\StoreManagerInterface $storeManager
     * @param \Comave\BigBuy\Service\Order\Tracking\Collector $orderTrackingCollectorr
     * @param string|null $name
     */
    public function __construct(
        private readonly Registry $registry,
        private readonly State $appState,
        private readonly StoreManagerInterface $storeManager,
        private readonly Collector $orderTrackingCollectorr,
        ?string $name = null
    ) {
        parent::__construct($name);
    }

    /**
     * @return void
     */
    protected function configure(): void
    {
        $this->setDescription('Retrieve BigBuy Brand List');
        parent::configure();
    }

    /**
     * @param \Symfony\Component\Console\Input\InputInterface $input
     * @param \Symfony\Component\Console\Output\OutputInterface $output
     * @return int
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        try {
            $this->setupProcess();
            $this->orderTrackingCollectorr->setOutput($output);
            $this->orderTrackingCollectorr->execute();

            return Cli::RETURN_SUCCESS;
        } catch (LocalizedException|Exception $e) {
            $output->writeln('Error:'.$e->getMessage());

            return Cli::RETURN_FAILURE;
        }
    }

    /**
     * Set process settings.
     * @throws LocalizedException
     */
    private function setupProcess(): void
    {
        $this->registry->register('isSecureArea', true);
        $this->appState->setAreaCode(Area::AREA_ADMINHTML);
        $this->storeManager->setCurrentStore(Store::DEFAULT_STORE_ID);
    }
}
