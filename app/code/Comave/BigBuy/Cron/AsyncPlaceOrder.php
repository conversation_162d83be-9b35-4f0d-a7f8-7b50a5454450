<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\BigBuy\Cron;

use Comave\BigBuy\Service\Order\AsyncProcessor;

class AsyncPlaceOrder
{

    public function __construct(
        private readonly AsyncProcessor $orderAsyncProcessor,
    ) {
    }

    /**
     * @return void
     */
    public function execute(): void
    {
        $this->orderAsyncProcessor->execute();
    }
}
