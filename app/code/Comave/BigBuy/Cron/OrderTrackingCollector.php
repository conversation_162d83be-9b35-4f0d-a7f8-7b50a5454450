<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\BigBuy\Cron;

use Comave\BigBuy\Service\Order\Tracking\Collector;

class OrderTrackingCollector
{
    /**
     * @param \Comave\BigBuy\Service\Order\Tracking\Collector $orderTrackingCollector
     */
    public function __construct(
        private readonly Collector $orderTrackingCollector,
    ) {
    }

    /**
     * @return void
     */
    public function execute(): void
    {
        $this->orderTrackingCollector->execute();
    }
}
