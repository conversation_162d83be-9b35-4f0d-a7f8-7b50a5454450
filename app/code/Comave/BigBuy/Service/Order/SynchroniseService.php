<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\BigBuy\Service\Order;

use Comave\BigBuy\Model\ConfigProvider;
use Comave\BigBuyShipping\Service\BigBuyShippingService;
use Comave\SellerApi\Api\ConfigurableApiInterface;
use Comave\SellerApi\Api\ConfigurableApiInterfaceFactory;
use Comave\SellerApi\Api\OrderSynchroniseInterface;
use Comave\SellerApi\Service\RequestHandler;
use Comave\SellerApi\Service\SellerIdentity;
use Exception;
use InvalidArgumentException;
use Laminas\Http\Request;
use Magento\Framework\Serialize\SerializerInterface;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Api\Data\OrderItemInterface;
use Magento\Sales\Model\Order\Pdf\InvoiceFactory;
use Psr\Http\Client\ClientExceptionInterface;
use Psr\Log\LoggerInterface;

class SynchroniseService implements OrderSynchroniseInterface
{
    public const string BIG_BUY_IDENTIFIER = 'bigbuy';

    /**
     * @param \Comave\BigBuy\Service\Order\LinkService $orderLinkService
     * @param \Comave\BigBuy\Service\Order\NotificationService $notificationService
     * @param \Comave\SellerApi\Api\ConfigurableApiInterfaceFactory $configurableApiFactory
     * @param \Comave\SellerApi\Service\SellerIdentity $sellerIdentity
     * @param \Magento\Framework\Serialize\SerializerInterface $serializer
     * @param \Comave\BigBuy\Model\ConfigProvider $configProvider
     * @param \Comave\SellerApi\Service\RequestHandler $requestHandler
     * @param \Psr\Log\LoggerInterface $logger
     */
    public function __construct(
        private readonly InvoiceFactory $invoicePdfFactory,
        private readonly LinkService $orderLinkService,
        private readonly NotificationService $notificationService,
        private readonly ConfigurableApiInterfaceFactory $configurableApiFactory,
        private readonly SellerIdentity $sellerIdentity,
        private readonly SerializerInterface $serializer,
        private readonly ConfigProvider $configProvider,
        private readonly RequestHandler $requestHandler,
        private readonly LoggerInterface $logger,
    ) {
    }

    /**
     * @param ConfigurableApiInterface $configurableApi
     * @param OrderInterface $order
     * @param OrderItemInterface[] $orderItems
     * @return void
     * @throws \Psr\Http\Client\ClientExceptionInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function synchroniseOrder(
        ConfigurableApiInterface $configurableApi,
        OrderInterface $order,
        array $orderItems
    ): void {
        if (!$this->configProvider->isOrderSyncEnable()) {
            $message = '[OrderSynchronizer] Not enabled, bypassing';
            $this->logger->info(
                $message,
                [
                    'seller' => self::BIG_BUY_IDENTIFIER,
                ]
            );
            $this->notificationService->addMessage((int)$order->getEntityId(), $message);

            return;
        }

        $seller = $this->sellerIdentity->identify(self::BIG_BUY_IDENTIFIER);
        if ($seller === null) {
            $message = '[OrderSynchronizer] Unable to identify seller';
            $this->logger->warning($message, ['seller' => self::BIG_BUY_IDENTIFIER]
            );
            $this->notificationService->addMessage((int)$order->getEntityId(), $message);

            return;
        }

        if (!$this->isFullSellerOrder($order, $orderItems)) {
            $message = '[OrderSynchronizer] Synchronization of an order with multiple sellers is not supported.';
            $this->logger->warning(
                $message,
                [
                    'seller' => self::BIG_BUY_IDENTIFIER,
                    'order' => $order->getIncrementId(),
                ]
            );
            $this->notificationService->addMessage((int)$order->getEntityId(), $message);

            return;
        }

        $balance = $this->verifyBalance($order);
        if (!$balance['can_place']) {
            $subject = sprintf('Unable to place order #%s', $order->getIncrementId());
            $this->notificationService->send($subject, $balance['message'], $balance);
            $message = sprintf('[OrderSynchronizer] %s', $balance['message']);
            $this->notificationService->addMessage(
                (int)$order->getEntityId(),
                sprintf('%s BigBuy Current Balance: %s', $message, $balance['amount'])
            );
            $this->logger->warning(
                $message,
                [
                    'seller' => self::BIG_BUY_IDENTIFIER,
                    'order' => $order->getIncrementId(),
                ]
            );

            return;
        }

        $orderSyncData = $this->extractSyncData($order, $orderItems);
        if (empty($orderSyncData)) {
            $message = '[OrderSynchronizer] Unable to identify seller products.';
            $this->logger->warning(
                $message,
                [
                    'seller' => self::BIG_BUY_IDENTIFIER,
                    'order' => $order->getIncrementId(),
                ]
            );
            $this->notificationService->addMessage((int)$order->getEntityId(), $message);

            return;
        }

        $configurableApi->setParams($this->serializer->serialize($orderSyncData));
        try {
            $results = $this->requestHandler->handleRequest($configurableApi);
            $content = $results->getResult()->getBody()->getContents();
            $decodedResult = [];
            if (!empty($content)) {
                $decodedResult = $this->serializer->unserialize($content);
            }

            if ($results->hasError()) {
                $message = 'There was an error processing the order';
                if (!empty($decodedResult['message'])) {
                    try {
                        $decodedMessage = $this->serializer->unserialize($decodedResult['message']);
                        $message = $decodedMessage['info'] ?? $message;
                    } catch (InvalidArgumentException) {
                        $message = $decodedResult['message'];
                    }
                }
                throw new Exception($message);
            }
            $this->orderLinkService->createLink((int)$order->getId(), $decodedResult['order_id'] ?: '');
            $message = '[OrderSynchronizer] Successfully synchronized order';
            $this->logger->info(
                $message,
                [
                    'order' => $order->getIncrementId(),
                    'response' => $decodedResult,
                ]
            );
            $this->notificationService->addMessage((int)$order->getEntityId(), $message);
            /** Try to send the PDF invoice to the seller system */
            if ($this->uploadInvoice($order, $decodedResult['order_id'])) {
                $this->notificationService->addMessage(
                    (int)$order->getEntityId(),
                    "[OrderSynchronizer] Successfully upload order invoice"
                );
            }
        } catch (Exception $e) {
            $this->notificationService->send(
                sprintf('Unable to place order #%s', $order->getIncrementId()),
                $e->getMessage(),
                $balance
            );
            $this->notificationService->addMessage(
                (int)$order->getEntityId(),
                sprintf('[OrderSynchronizer] Unable to place order: %s', $e->getMessage())
            );
            $this->logger->error(
                '[OrderSynchronizer] Unable to synchronize order.',
                [
                    'error' => $e->getMessage(),
                    'order' => $order->getIncrementId(),
                ]
            );
        }
    }

    /**
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @param array $orderItems
     * @return bool
     */
    private function isFullSellerOrder(OrderInterface $order, array $orderItems): bool
    {
        return count($order->getItems()) === count($orderItems);
    }

    /**
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @param array $items
     * @return array[]
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function extractSyncData(OrderInterface $order, array $items): array
    {
        $orderData = [];
        $orderData['internalReference'] = $order->getIncrementId();
        $orderData['language'] = 'en';
        $orderData['paymentMethod'] = $order->getPayment()->getMethod();
        $orderData['carriers'] = [
            [
                'name' => BigBuyShippingService::getBigbuyMethodCodeFromShippingMethod($order->getShippingMethod()),
            ],
        ];
        $orderData['shippingAddress'] = [
            'firstName' => $order->getShippingAddress()->getFirstname(),
            'lastName' => $order->getShippingAddress()->getLastname(),
            'country' => $order->getShippingAddress()->getCountryId(),
            'postcode' => $order->getShippingAddress()->getPostcode() ?? '',
            'town' => $order->getShippingAddress()->getCity() ?? '',
            'address' => implode(', ', $order->getShippingAddress()->getStreet() ?? ''),
            'phone' => $order->getShippingAddress()->getTelephone() ?? '',
            'email' => $order->getShippingAddress()->getEmail() ?? '',
            'vatNumber' => $order->getShippingAddress()->getVatId() ?? '',
            'companyName' => $order->getShippingAddress()->getCompany() ?? '',
            'comment' => $order->getShippingAddress()->getComment() ?? '',
        ];

        $products = [];
        foreach ($items as $item) {
            $product = [];
            $product['reference'] = $item->getSku();
            $product['quantity'] = (int)$item->getQtyOrdered();
            $products[] = $product;
        }
        $orderData['products'] = $products;

        return ['order' => $orderData];
    }

    /**
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @return array
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    private function verifyBalance(OrderInterface $order): array
    {
        $balance = [
            'amount' => 0,
            'can_place' => false,
            'message' => 'Unable to verify balance',
        ];
        try {
            $configurableApi = $this->configurableApiFactory->create([
                'method' => Request::METHOD_GET,
                'endpoint' => $this->configProvider->getVerifyBalanceEndpoint(),
                'headers' => [
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                    'Authorization' => sprintf('Bearer %s', $this->configProvider->getApiKey()),
                ],
            ]);
            $response = $this->requestHandler->handleRequest($configurableApi);
            if (!$response->hasError()) {
                $result = $this->serializer->unserialize(
                    $response->getResult()->getBody()->getContents()
                );
                $balance['amount'] = (float)$result;
                $balance['can_place'] = (float)$result >= (float)$order->getGrandTotal();
                $balance['message'] = $balance['can_place'] ?
                    'The BigBuy balance is normal.' :
                    'Unable to place order because the BigBuy balance is too low.';
            }

            return $balance;
        } catch (ClientExceptionInterface|Exception $exception) {
            $message = 'Unable to verify balance';
            $this->logger->warning($message, [
                'exception' => $exception->getMessage(),
                'order_id' => $order->getIncrementId(),
            ]);
            $this->notificationService->addMessage((int)$order->getEntityId(), $message);
        }

        return $balance;
    }

    /**
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @param string $sellerOrderId
     * @return bool
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    private function uploadInvoice(OrderInterface $order, string $sellerOrderId): bool
    {
        if (!$this->isNonEuShippingCountry($order)) {
            return false;
        }

        try {
            $pdfInvoice = $this->invoicePdfFactory->create()->getPdf($order->getInvoiceCollection()->getItems());
            $file = base64_encode($pdfInvoice->render());

            $configurableApi = $this->configurableApiFactory->create([
                'method' => Request::METHOD_POST,
                'endpoint' => $this->configProvider->getUploadInvoiceEndpoint(),
                'headers' => [
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                    'Authorization' => sprintf('Bearer %s', $this->configProvider->getApiKey()),
                ],
                'params' => $this->serializer->serialize([
                    'idOrder' => $sellerOrderId,
                    'file' => $file,
                    'mimeType' => 'application/pdf',
                    'amount' => $order->getTotalInvoiced(),
                    'concept' => sprintf("Invoice for Order number %s", $sellerOrderId),
                ]),
            ]);
            $response = $this->requestHandler->handleRequest($configurableApi);
            $result = $this->serializer->unserialize(
                $response->getResult()->getBody()->getContents()
            );
            if (!$response->hasError()) {
                $message = '[OrderSynchronizer] Successfully upload invoice order';
                $this->logger->info(
                    $message,
                    [
                        'order' => $order->getIncrementId(),
                        'response' => $result,
                    ]
                );
                $this->notificationService->addMessage((int)$order->getEntityId(), $message);
            } else {
                $message = match ((int)$result['code']) {
                    400 => 'Validation errors has been found when uploading the invoice.',
                    409 => 'There are constrain conflicts.',
                    415 => 'Invalid Content-Type header.',
                    429 => 'Exceeded requests limits.',
                    default => 'There was an error uploading the invoice for the order.',
                };
                if (!empty($result['message'])) {
                    $message = sprintf("%s %s", $message, $result['message']);
                }
                throw new Exception($message);
            }

            return true;
        } catch (ClientExceptionInterface|Exception $exception) {
            $message = '[OrderSynchronizer] Unable to upload invoice: %s';
            $this->logger->warning($message, [
                'exception' => $exception->getMessage(),
                'order_id' => $order->getIncrementId(),
            ]);
            $this->notificationService->addMessage(
                (int)$order->getEntityId(),
                sprintf($message, $exception->getMessage())
            );

            return false;
        }
    }

    /**
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @return bool
     */
    private function isNonEuShippingCountry(OrderInterface $order): bool
    {
        $euCountries = $this->configProvider->getEuCountries();
        $shippingCountry = $order->getShippingAddress()->getCountryId();

        return !in_array($shippingCountry, explode(',', $euCountries));
    }
}
