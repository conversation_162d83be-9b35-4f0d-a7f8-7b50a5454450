<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\BigBuy\Service\Order;

use Comave\BigBuy\Model\ConfigProvider;
use Comave\Sales\Model\Order\NotificationUiManager;
use Exception;
use Magento\Framework\App\Area;
use Magento\Framework\Mail\Template\TransportBuilder;
use Magento\Framework\Translate\Inline\StateInterface;
use Magento\Store\Model\Store;
use Psr\Log\LoggerInterface;

class NotificationService
{
    private const string EMAIL_TEMPLATE_IDENTIFIER = 'bigbuy_place_order_notification';

    /**
     * @param \Comave\BigBuy\Model\ConfigProvider $configProvider
     * @param \Magento\Framework\Mail\Template\TransportBuilder $transportBuilder
     * @param \Magento\Framework\Translate\Inline\StateInterface $inlineTranslation
     * @param \Psr\Log\LoggerInterface $logger
     */
    public function __construct(
        private readonly NotificationUiManager $notificationUiManager,
        private readonly ConfigProvider $configProvider,
        private readonly TransportBuilder $transportBuilder,
        private readonly StateInterface $inlineTranslation,
        private readonly LoggerInterface $logger,
    ) {
    }

    /**
     * @param string $subject
     * @param string $message
     * @param array $balance
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\MailException
     */
    public function send(string $subject, string $message, array $balance = []): void
    {
        $sender = [
            'name' => $this->configProvider->getNotificationSenderName(),
            'email' => $this->configProvider->getNotificationSenderEmail(),
        ];
        $this->inlineTranslation->suspend();
        $vars = [
            'subject' => $subject,
            'message' => $message,
            'balance' => $balance['amount'],
        ];

        $transporter = $this->transportBuilder->setTemplateIdentifier(
            self::EMAIL_TEMPLATE_IDENTIFIER
        )->setTemplateOptions(
            [
                'area' => Area::AREA_FRONTEND,
                'store' => Store::DEFAULT_STORE_ID,
            ]
        )->setTemplateVars($vars)->setFromByScope($sender);

        $transporter->addTo($this->configProvider->getNotificationSupportEmail());
        $transporter->addTo($this->configProvider->getNotificationAdminEmail());
        $transport = $transporter->getTransport();

        try {
            $transport->sendMessage();
        } catch (Exception $e) {
            $this->logger->critical('Cannot send place order notification', [
                'message' => $e->getMessage(),
            ]);
        }
        $this->inlineTranslation->resume();
    }

    /**
     * @param int $orderId
     * @param string $message
     * @return void
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function addMessage(int $orderId, string $message): void
    {
        $notification = $this->notificationUiManager->get(null);
        $notification->setOrderId($orderId);
        $notification->setMessage($message);
        $this->notificationUiManager->save($notification);
    }
}
