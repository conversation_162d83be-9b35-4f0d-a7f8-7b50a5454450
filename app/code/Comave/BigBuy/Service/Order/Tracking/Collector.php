<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\BigBuy\Service\Order\Tracking;

use Comave\BigBuy\Model\OrderLinkUiManager;
use Comave\BigBuy\Service\Order\SynchroniseService;
use Comave\Sales\Model\Order\TrackingUiManager;
use Comave\SellerApi\Service\SellerIdentity;
use Generator;
use Magento\Framework\Serialize\SerializerInterface;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Helper\ProgressBarFactory;
use Symfony\Component\Console\Output\OutputInterface;

class Collector
{
    private const int BATCH_SIZE = 1000;
    /**
     * @var \Symfony\Component\Console\Output\OutputInterface|null
     */
    private ?OutputInterface $output = null;

    /**
     * @param \Symfony\Component\Console\Helper\ProgressBarFactory $progressBarFactory
     * @param \Comave\BigBuy\Service\Order\Tracking\Fetcher $orderTrackingFetcherService
     */
    public function __construct(
        private readonly SerializerInterface $serializer,
        private readonly TrackingUiManager $trackingUiManager,
        private readonly SellerIdentity $sellerIdentity,
        private readonly OrderLinkUiManager $orderLinkUiManager,
        private readonly ProgressBarFactory $progressBarFactory,
        private readonly Fetcher $orderTrackingFetcherService,
    ) {
    }

    /**
     * @return void
     */
    public function execute(): void
    {
        $sellerId = (int)$this->sellerIdentity->identify(SynchroniseService::BIG_BUY_IDENTIFIER);
        foreach ($this->getBatches() as $batch) {
            $this->process($sellerId, $batch);
        }
    }

    /**
     * @param null|OutputInterface $output
     */
    public function setOutput(?OutputInterface $output): void
    {
        $this->output = $output;
    }

    /**
     * @return \Symfony\Component\Console\Output\OutputInterface|null
     */
    private function getOutput(): ?OutputInterface
    {
        return $this->output;
    }

    /**
     * @param int $dimension
     * @return \Symfony\Component\Console\Helper\ProgressBar|null
     */
    private function getProgressBar(int $dimension = 0): ?ProgressBar
    {
        $progressBar = null;
        if ($this->hasOutput()) {
            $progressBar = $this->progressBarFactory->create([
                'output' => $this->getOutput(),
                'max' => $dimension,
            ]);
            $progressBar->setBarCharacter('<fg=green>⚬</>');
            $progressBar->setEmptyBarCharacter("<fg=red>⚬</>");
            $progressBar->setProgressCharacter("<fg=green>➤</>");
        }

        return $progressBar;
    }

    /**
     * @return bool
     */
    private function hasOutput(): bool
    {
        return !is_null($this->output);
    }

    /**
     * @param int $sellerId
     * @param array $batch
     * @return void
     */
    private function process(int $sellerId, array $batch): void
    {
        sleep(1);
        $orderTrackings = $this->orderTrackingFetcherService->fetch($batch);
        if ($this->hasOutput()) {
            $this->output->writeln(
                sprintf(
                    "<info>%s</info>",
                    __('Collecting BigBuy order tracking numbers ...')
                )
            );
            $this->getOutput()->writeln("");
            $progressBar = $this->getProgressBar(count($orderTrackings));
            $progressBar->start();
            $progressBar->display();
        }

        foreach ($orderTrackings as $orderTracking) {
            foreach ($orderTracking['trackings'] as $tracking) {
                $sellerOrderTracking = $this->trackingUiManager->identify(
                    $sellerId,
                    (int)$batch[$orderTracking['id']],
                    $tracking['trackingNumber']
                );
                $sellerOrderTracking->setSellerId($sellerId);
                $sellerOrderTracking->setOrderId((int)$batch[$orderTracking['id']]);
                $sellerOrderTracking->setTrackingNumber($tracking['trackingNumber']);
                $sellerOrderTracking->setTrackingStatus($tracking['statusDescription']);
                $sellerOrderTracking->setDate($tracking['statusDate']);
                $sellerOrderTracking->setPayload($this->serializer->serialize($tracking));
                $this->trackingUiManager->save($sellerOrderTracking);
            }
            if ($this->hasOutput() && !empty($progressBar)) {
                $progressBar->advance();
            }
        }

        if ($this->hasOutput() && !empty($progressBar)) {
            $progressBar->finish();
            $this->getOutput()->writeln("\n");
            $this->getOutput()->writeln(
                sprintf(
                    '<info>%s</info>',
                    __('BigBuy order tracking numbers have been collected successfully')
                )
            );
        }
    }

    /**
     * @return \Generator
     */
    private function getBatches(): Generator
    {
        $page = 1;
        list($orderLinks, $pages) = $this->orderLinkUiManager->getLinks($page, self::BATCH_SIZE);
        do {
            $batch = [];
            foreach ($orderLinks as $orderLink) {
                $batch[$orderLink->getBigBuyOrderId()] = $orderLink->getOrderId();
            }
            yield $batch;
            $page++;
            list($orderLinks, $pages) = $this->orderLinkUiManager->getLinks($page, self::BATCH_SIZE);
        } while ($pages > $page);
    }
}
