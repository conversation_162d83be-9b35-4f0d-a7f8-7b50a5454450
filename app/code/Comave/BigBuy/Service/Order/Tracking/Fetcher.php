<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\BigBuy\Service\Order\Tracking;

use Comave\BigBuy\Model\ConfigProvider;
use Comave\SellerApi\Api\ConfigurableApiInterfaceFactory;
use Comave\SellerApi\Service\RequestHandler;
use Exception;
use Laminas\Http\Request;
use Magento\Framework\Serialize\SerializerInterface;
use Psr\Http\Client\ClientExceptionInterface;
use Psr\Log\LoggerInterface;

class Fetcher
{
    /**
     * @param \Magento\Framework\Serialize\SerializerInterface $serializer
     * @param \Psr\Log\LoggerInterface $logger
     * @param \Comave\SellerApi\Service\RequestHandler $requestHandler
     * @param \Comave\BigBuy\Model\ConfigProvider $configProvider
     * @param \Comave\SellerApi\Api\ConfigurableApiInterfaceFactory $configurableApiFactory
     */
    public function __construct(
        private readonly SerializerInterface $serializer,
        private readonly LoggerInterface $logger,
        private readonly RequestHandler $requestHandler,
        private readonly ConfigProvider $configProvider,
        private readonly ConfigurableApiInterfaceFactory $configurableApiFactory,
    ) {
    }

    /**
     * @return array
     */
    public function fetch(array $orders): array
    {
        if (!$this->configProvider->isOrderTrackingSyncEnable()) {
            $this->logger->warning('BigBuy Order Tracking Sync process is disabled');

            return [];
        }

        if (empty($this->configProvider->getApiEndpoint())) {
            $this->logger->warning('BigBuy API endpoint is not configured');

            return [];
        }
        $params = [
            'isoCode' => 'en',
            'track' => [
                'orders' => [],
            ],
        ];
        foreach (array_keys($orders) as $order) {
            $params['track']['orders'][] = ['id' => (string)$order];
        }

        $result = [];
        try {
            $configurableApi = $this->configurableApiFactory->create([
                'method' => Request::METHOD_POST,
                'endpoint' => $this->configProvider->getOrderTrackingSyncEndpoint(),
                'headers' => [
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                    'Authorization' => sprintf('Bearer %s', $this->configProvider->getApiKey()),
                ],
                'params' => $this->serializer->serialize($params),
            ]);
            $response = $this->requestHandler->handleRequest($configurableApi);
            if (!$response->hasError()) {
                $result = $this->serializer->unserialize(
                    $response->getResult()->getBody()->getContents()
                );
            }
        } catch (Exception|ClientExceptionInterface $exception) {
            $this->logger->critical("Cannot retrieve order tracking numbers", [
                'reason' => $exception->getMessage(),
            ]);
        }

        return $result;
    }
}
