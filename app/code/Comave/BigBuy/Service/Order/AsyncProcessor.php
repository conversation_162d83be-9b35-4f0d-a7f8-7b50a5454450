<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\BigBuy\Service\Order;

use Comave\SellerApi\Api\OrderSyncMessageInterface;
use Comave\SellerApi\Api\OrderSyncMessageInterfaceFactory;
use Comave\SellerApi\Model\Queue\Consumer\OrderSync;
use Comave\SellerApi\Service\OrderSellersIdentifier;
use Magento\Framework\Encryption\EncryptorInterface;
use Magento\Framework\Event\ManagerInterface;
use Magento\Framework\MessageQueue\PublisherInterface;
use Magento\Framework\Serialize\SerializerInterface;
use Magento\Sales\Model\ResourceModel\Order\CollectionFactory;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Helper\ProgressBarFactory;
use Symfony\Component\Console\Output\OutputInterface;

class AsyncProcessor
{
    private const int BATCH_SIZE = 10;

    private array $dispatched = [];

    /**
     * @var \Symfony\Component\Console\Output\OutputInterface|null
     */
    private ?OutputInterface $output = null;

    /**
     * @param \Symfony\Component\Console\Helper\ProgressBarFactory $progressBarFactory
     * @param \Magento\Sales\Model\ResourceModel\Order\CollectionFactory $orderCollectionFactory
     * @param \Psr\Log\LoggerInterface $logger
     * @param \Comave\SellerApi\Service\OrderSellersIdentifier $orderSellersIdentifier
     * @param \Comave\BigBuy\Service\Order\OrderSyncMessageInterfaceFactory $messageFactory
     * @param \Magento\Framework\Encryption\EncryptorInterface $encryptor
     * @param \Magento\Framework\Serialize\SerializerInterface $serializer
     * @param \Magento\Framework\MessageQueue\PublisherInterface $publisher
     * @param \Magento\Framework\Event\ManagerInterface $eventManager
     */
    public function __construct(
        private readonly ProgressBarFactory $progressBarFactory,
        private readonly CollectionFactory $orderCollectionFactory,
        private readonly LoggerInterface $logger,
        private readonly OrderSellersIdentifier $orderSellersIdentifier,
        private readonly OrderSyncMessageInterfaceFactory $messageFactory,
        private readonly EncryptorInterface $encryptor,
        private readonly SerializerInterface $serializer,
        private readonly PublisherInterface $publisher,
        private readonly ManagerInterface $eventManager,

    ) {
    }

    /**
     * @return void
     */
    public function execute(): void
    {
        if ($this->hasOutput()) {
            $this->getOutput()->writeln(
                sprintf(
                    "<info>%s</info>",
                    __("Fetching the orders in groups of %1", self::BATCH_SIZE)
                )
            );
        }
        $batches = $this->getBatches();
        if ($this->hasOutput()) {
            $this->getOutput()->writeln(
                sprintf(
                    "<info>%s</info>",
                    __("Processing %1 batches having %2 orders per batch", count($batches), self::BATCH_SIZE),
                )
            );
            $this->getOutput()->writeln("");
            $progressBar = $this->getProgressBar(count($batches));
            $progressBar->start();
            $progressBar->display();
        }

        foreach ($batches as $batch) {
            foreach ($batch as $order) {
                $sellerItems = $this->orderSellersIdentifier->get($order);
                if (empty($sellerItems)) {
                    $this->logger->error(
                        '[SellerOrderSync] Unable to process order, no seller products detected',
                        [
                            'order' => $order->getIncrementId(),
                        ]
                    );
                    continue;
                }
                foreach ($sellerItems as $sellerIdentifier => $items) {
                    if (empty($items) || SynchroniseService::BIG_BUY_IDENTIFIER !== $sellerIdentifier) {
                        continue;
                    }
                    $this->process($sellerIdentifier, $order, $items);
                }
            }
            if ($this->hasOutput() && !empty($progressBar)) {
                $progressBar->advance();
            }
        }

        if ($this->hasOutput() && !empty($progressBar)) {
            $progressBar->finish();
            $this->getOutput()->writeln("\n");
            $this->getOutput()->writeln(
                sprintf(
                    '<info>%s</info>',
                    __('BigBuy orders have been processed successfully')
                )
            );
        }
    }

    /**
     * @return array
     */
    private function getBatches(): array
    {
        $dataSource = $this->getUnsyncedOrders();

        return array_chunk($dataSource, self::BATCH_SIZE);
    }

    /**
     * @return array
     */
    private function getUnsyncedOrders(): array
    {
        $collection = $this->orderCollectionFactory->create()
            ->addAttributeToSelect('*')
            ->addFieldToFilter(
                'status',
                ['nin' => ['canceled', 'closed', 'complete', 'delivered', 'rejected', 'order_accepted', 'fraud']]
            );

        $collection->getSelect()
            ->joinLeft(
                ["cbo" => "comave_bigbuy_order"],
                'main_table.entity_id = cbo.order_id',
                ['link_id']
            )->where('cbo.link_id IS NULL');

        return $collection->getSize() ? $collection->getItems() : [];
    }

    /**
     * @param null|OutputInterface $output
     */
    public function setOutput(?OutputInterface $output): void
    {
        $this->output = $output;
    }

    /**
     * @return \Symfony\Component\Console\Output\OutputInterface|null
     */
    private function getOutput(): ?OutputInterface
    {
        return $this->output;
    }

    /**
     * @param int $dimension
     * @return \Symfony\Component\Console\Helper\ProgressBar|null
     */
    private function getProgressBar(int $dimension = 0): ?ProgressBar
    {
        $progressBar = null;
        if ($this->hasOutput()) {
            $progressBar = $this->progressBarFactory->create([
                'output' => $this->getOutput(),
                'max' => $dimension,
            ]);
            $progressBar->setBarCharacter('<fg=green>⚬</>');
            $progressBar->setEmptyBarCharacter("<fg=red>⚬</>");
            $progressBar->setProgressCharacter("<fg=green>➤</>");
        }

        return $progressBar;
    }

    /**
     * @return bool
     */
    private function hasOutput(): bool
    {
        return !is_null($this->output);
    }

    /**
     * @param string $sellerIdentifier
     * @param string $itemIds
     * @return string
     */
    private function getDispatchKey(string $sellerIdentifier, string $itemIds): string
    {
        return $this->encryptor->encrypt(
            $sellerIdentifier.$itemIds
        );
    }

    /**
     * @param string $sellerIdentifier
     * @param $order
     * @param array $items
     * @return void
     */
    private function process(string $sellerIdentifier, $order, array $items): void
    {
        $publishHash = $this->getDispatchKey($sellerIdentifier, implode(',', $items));
        if (in_array($publishHash, $this->dispatched)) {
            $this->logger->warning(
                '[SellerOrderSync] Queue sync order already dispatched',
                [
                    'order' => $order->getEntityId(),
                    'sellerIdentifier' => $sellerIdentifier,
                    'items' => $items,
                ]
            );

            return;
        }

        $this->dispatched[] = $publishHash;
        /** @var OrderSyncMessageInterface $message */
        $message = $this->messageFactory->create();
        $message->setSellerIdentifier($sellerIdentifier)
            ->setOrderId((string)$order->getEntityId())
            ->setItems($this->serializer->serialize($items));

        $this->eventManager->dispatch(
            'before_order_sync_queue_dispatch',
            [
                'message' => $message,
                'order' => $order,
            ]
        );

        $this->publisher->publish(
            OrderSync::TOPIC_NAME,
            $message
        );

        $this->logger->info(
            '[SellerOrderSync] Published queue sync order',
            [
                'order' => $order->getEntityId(),
                'sellerIdentifier' => $sellerIdentifier,
                'items' => $items,
            ]
        );
    }
}
