<?php
     $marketplaceHelper = $block->getMpHelper();
     $isPartner= $marketplaceHelper->isSeller();
     $isEnable = false;
     $magentoCurrentUrl = $block->getCurrentUrl();
?>
<?php if ($isPartner): ?>
    <?php if ($isEnable): ?>
        <li id="wk-mp-menu-coditron_customshippingrate" class="wk-mp-menu-coditron_customshippingrate level-0
            <?=  $escaper->escapeUrl(strpos($magentoCurrentUrl, 'coditron_customshippingrate/shiptablerates/') ? "current active":"");?>">
            <a
                href="<?=  $escaper->escapeUrl($block->getUrl('coditron_customshippingrate/shiptablerates/manage', ['_secure' => 1])); ?>"
                class="<?=  $escaper->escapeUrl(strpos($magentoCurrentUrl, 'coditron_customshippingrate/shiptablerates/')? "active":"");?>">
                <?=  $escaper->escapeHtml(__('Manage Shipping Methods')) ?>
            </a>
        </li>

    <?php endif; ?>
<?php endif; ?>
