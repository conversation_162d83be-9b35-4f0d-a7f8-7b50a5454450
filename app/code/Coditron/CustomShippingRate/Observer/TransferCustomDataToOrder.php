<?php
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Observer;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;

class TransferCustomDataToOrder implements ObserverInterface
{
    public function execute(Observer $observer)
    {
        $quote = $observer->getEvent()->getQuote();
        $order = $observer->getEvent()->getOrder();

        $order->setData('shipping_data', $quote->getShippingData());
    }
}
