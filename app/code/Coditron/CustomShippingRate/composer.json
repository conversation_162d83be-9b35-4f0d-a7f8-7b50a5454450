{"name": "coditron/magento2-customshippingrate", "description": "Set your own Custom Shipping rates for admin order or create predefined shipping methods for frontend customers", "keywords": ["magento 2", "magento2", "shipping", "magento2 how to create custom shipping method", "magento2 shipping module", "magento2 shipping extension", "custom shipping module", "shipping rates", "shipping extension", "shipping method", "custom shipping", "magento 2 free shipping extension", "magento shipping", "free shipping"], "type": "magento2-module", "version": "1.8.1", "license": ["proprietary"], "homepage": "https://www.coditron.com/", "support": {"email": "<EMAIL>", "issues": "https://github.com/coditron/magento2-custom-shipping-rate/issues/"}, "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.coditron.com/", "role": "Leader"}], "require": {"php": "^8.1.0", "magento/module-backend": "102.0.*", "magento/framework": "103.0.*", "webkul/marketplace-seller-category": "*", "magento/module-quote": "*", "magento/module-customer": "*", "magento/module-directory": "*", "magento/module-sales": "*", "magento/module-config": "*", "magento/module-shipping": "*", "magento/module-ui": "*", "magento/module-catalog": "*", "magento/module-checkout": "*", "magento/module-store": "*", "webkul/module-marketplace": "*"}, "autoload": {"files": ["registration.php"], "psr-4": {"Coditron\\CustomShippingRate\\": ""}}}