<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Co<PERSON>ron\CustomShippingRate\Model;

use Coditron\CustomShippingRate\Api\Data\ShipTableRatesInterface;
use Coditron\CustomShippingRate\Api\Data\ShipTableRatesInterfaceFactory;
use Coditron\CustomShippingRate\Api\Data\ShipTableRatesSearchResultsInterfaceFactory;
use Coditron\CustomShippingRate\Api\Data\ShipTableRatesSearchResultsInterface;
use Coditron\CustomShippingRate\Api\ShipTableRatesRepositoryInterface;
use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates as ResourceShipTableRates;
use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory as ShipTableRatesCollectionFactory;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;

class ShipTableRatesRepository implements ShipTableRatesRepositoryInterface
{
    /**
     * @param ResourceShipTableRates $resource
     * @param ShipTableRatesInterfaceFactory $shipTableRatesFactory
     * @param ShipTableRatesCollectionFactory $shipTableRatesCollectionFactory
     * @param ShipTableRatesSearchResultsInterfaceFactory $searchResultsFactory
     * @param CollectionProcessorInterface $collectionProcessor
     */
    public function __construct(
        protected ResourceShipTableRates $resource,
        protected ShipTableRatesInterfaceFactory $shipTableRatesFactory,
        protected ShipTableRatesCollectionFactory $shipTableRatesCollectionFactory,
        protected ShipTableRatesSearchResultsInterfaceFactory $searchResultsFactory,
        protected CollectionProcessorInterface $collectionProcessor
    ) { }

    /**
     * @inheritDoc
     */
    public function save(ShipTableRatesInterface $shipTableRates): ShipTableRatesInterface
    {
        try {
            $this->resource->save($shipTableRates);
        } catch (\Exception $exception) {
            throw new CouldNotSaveException(__(
                'Could not save the shipTableRates: %1',
                $exception->getMessage()
            ));
        }
        return $shipTableRates;
    }

    /**
     * @inheritDoc
     */
    public function get(int $shipTableRatesId): ShipTableRatesInterface
    {
        $shipTableRates = $this->shipTableRatesFactory->create();
        $this->resource->load($shipTableRates, $shipTableRatesId);
        if (!$shipTableRates->getId()) {
            throw new NoSuchEntityException(__('ShipTableRates with id "%1" does not exist.', $shipTableRatesId));
        }
        return $shipTableRates;
    }

    /**
     * @inheritDoc
     */
    public function getList(
        \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
    ): ShipTableRatesSearchResultsInterface {
        $collection = $this->shipTableRatesCollectionFactory->create();

        $this->collectionProcessor->process($searchCriteria, $collection);

        $searchResults = $this->searchResultsFactory->create();
        $searchResults->setSearchCriteria($searchCriteria);

        $items = [];
        foreach ($collection as $model) {
            $items[] = $model;
        }

        $searchResults->setItems($items);
        $searchResults->setTotalCount($collection->getSize());
        return $searchResults;
    }

    /**
     * @inheritDoc
     */
    public function delete(ShipTableRatesInterface $shipTableRates): bool
    {
        try {
            $shipTableRatesModel = $this->shipTableRatesFactory->create();
            $this->resource->load($shipTableRatesModel, $shipTableRates->getShiptableratesId());
            $this->resource->delete($shipTableRatesModel);
        } catch (\Exception $exception) {
            throw new CouldNotDeleteException(__(
                'Could not delete the ShipTableRates: %1',
                $exception->getMessage()
            ));
        }
        return true;
    }

    /**
     * @inheritDoc
     */
    public function deleteById(int $shipTableRatesId): bool
    {
        return $this->delete($this->get($shipTableRatesId));
    }
}

