<?php

namespace Coditron\CustomShippingRate\Ui\Component\Listing\Column;

/**
 * @deprecated to remove
 */
class Parcel implements \Magento\Framework\Option\ArrayInterface
{
    //Here you can __construct Model

    /**
     * @deprecated to remove
     * @return array[]
     */
    public function toOptionArray(): array
    {
        return [
            ['value' => 'S', 'label' => 'Small parcel'],
            ['value' => 'M', 'label' => 'Medium parcel'],
            ['value' => 'L', 'label' => 'Large parcel']
        ];
    }
}
