<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_MpSellerCategory
 * <AUTHOR> Software Private Limited
 * @copyright Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
namespace Coditron\CustomShippingRate\Controller\ShipTableRates;

class Save extends \Coditron\CustomShippingRate\Controller\AbstractShiprate
{
    /**
     * Seller Category Save action
     *
     * @return \Magento\Framework\Controller\Result\RedirectFactory
     */
    public function execute()
    {
        /** @var \Magento\Framework\Controller\Result\Redirect $resultRedirect */
        if ($this->getRequest()->isPost()) {
            try {
                if (!$this->_formKeyValidator->validate($this->getRequest())) {
                    return $this->resultRedirectFactory->create()->setPath(
                        'coditron_customshippingrate/shiptablerates/manage',
                        ['_secure' => $this->getRequest()->isSecure()]
                    );
                }

                $postData = $this->getRequest()->getPostValue();
                $postData['seller_id'] = $this->getSellerId();
                $result = $this->_helper->validateData($postData);
                if ($result['error']) {
                    $this->messageManager->addError(__($result['msg']));
                    return $this->resultRedirectFactory->create()->setPath(
                        'coditron_customshippingrate/shiptablerates/manage',
                        ['_secure' => $this->getRequest()->isSecure()]
                    );
                }

                $sellerShiprate = $this->getSellerShiprate();


                if ($postData['id']) {
                    $sellerShiprate->addData($postData)->setShiptableratesId($postData['id']);
                } else {
                    $sellerShiprate->setData($postData);
                }

                $sellerShiprate->save();
                $id = $sellerShiprate->getShiptableratesId();
                $this->messageManager->addSuccess(__("Shipping Rate saved successfully."));
                $this->_helper->clearCache();
                return $this->resultRedirectFactory->create()->setPath(
                    'coditron_customshippingrate/shiptablerates/edit',
                    ['shiptablerates_id' => $id, '_secure' => $this->getRequest()->isSecure()]
                );
            } catch (\Exception $e) {
                $this->messageManager->addError($e->getMessage());
                return $this->resultRedirectFactory->create()->setPath(
                    'coditron_customshippingrate/shiptablerates/manage',
                    ['_secure' => $this->getRequest()->isSecure()]
                );
            }
        } else {
            return $this->resultRedirectFactory->create()->setPath(
                'coditron_customshippingrate/shiptablerates/manage',
                ['_secure' => $this->getRequest()->isSecure()]
            );
        }
    }
}
