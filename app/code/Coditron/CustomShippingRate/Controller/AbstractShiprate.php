<?php

namespace Co<PERSON>ron\CustomShippingRate\Controller;

use Magento\Framework\App\Action\Context;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Controller\Result\ForwardFactory;
use Magento\Framework\Data\Form\FormKey\Validator as FormKeyValidator;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\View\Result\PageFactory;
use Webkul\Marketplace\Helper\Data as MarketplaceHelper;
use Coditron\CustomShippingRate\Helper\Data as HelperData;

abstract class AbstractShiprate extends \Magento\Framework\App\Action\Action
{
    /**
     * @var _resultPageFactory
     */
    protected $_resultPageFactory;

    /**
     * @var ForwardFactory
     */
    protected $resultForwardFactory;

    /**
     * @var \Magento\Customer\Model\Url
     */
    protected $_url;

    /**
     * @var \Magento\Customer\Model\Session
     */
    protected $_customerSession;

    /**
     * @var \Magento\Framework\Data\Form\FormKey\Validator
     */
    protected $_formKeyValidator;

    /**
     * @var SubAccount
     */
    protected $_subAccount;

    /**
     * @var \Magento\Framework\Stdlib\DateTime\DateTime
     */
    protected $_date;

    /**
     * @var SubAccountRepositoryInterface
     */
    protected $_subAccountRepository;

    /**
     * @var MarketplaceHelper
     */
    protected $_marketplaceHelper;

    /**
     * @var HelperData
     */
    protected $_helper;

    /**
     * @var \Magento\Framework\Registry
     */
    protected $_registry;

    /**
     * @var \Coditron\CustomShippingRate\Model\ShipTableRatesFactory
     */
    protected $mpSellershiprateFactory;

    /**
     * @var \Coditron\CustomShippingRate\Api\ShipTableRatesRepositoryInterface
     */
    protected $shiprateRepository;

    /**
     * @param Context $context
     * @param PageFactory $resultPageFactory
     * @param ForwardFactory $resultForwardFactory
     * @param \Magento\Customer\Model\Url $url
     * @param \Magento\Customer\Model\Session $customerSession
     * @param FormKeyValidator $formKeyValidator
     * @param MarketplaceHelper $marketplaceHelper
     * @param HelperData $helper
     * @param \Magento\Framework\Registry $registry
     * @param \Coditron\CustomShippingRate\Model\ShipTableRatesFactory $mpSellershiprateFactory
     * @param \Coditron\CustomShippingRate\Api\ShipTableRatesRepositoryInterface $shiprateRepository
     */
    public function __construct(
        Context $context,
        PageFactory $resultPageFactory,
        ForwardFactory $resultForwardFactory,
        \Magento\Customer\Model\Url$url,
        \Magento\Customer\Model\Session$customerSession,
        FormKeyValidator $formKeyValidator,
        MarketplaceHelper $marketplaceHelper,
        HelperData $helper,
        \Magento\Framework\Registry $registry,
        \Coditron\CustomShippingRate\Model\ShipTableRatesFactory $mpSellershiprateFactory,
        \Coditron\CustomShippingRate\Api\ShipTableRatesRepositoryInterface $shiprateRepository
    ) {
        $this->_resultPageFactory = $resultPageFactory;
        $this->resultForwardFactory = $resultForwardFactory;
        $this->_url = $url;
        $this->_customerSession = $customerSession;
        $this->_formKeyValidator = $formKeyValidator;
        $this->_marketplaceHelper = $marketplaceHelper;
        $this->_helper = $helper;
        $this->_registry = $registry;
        $this->mpSellershiprateFactory = $mpSellershiprateFactory;
        $this->shiprateRepository = $shiprateRepository;
        parent::__construct($context);
    }

    /**
     * Check authentication.
     *
     * @param RequestInterface $request
     *
     * @return \Magento\Framework\App\ResponseInterface
     */
    public function dispatch(RequestInterface $request)
    {
        if (!$this->_helper->isAllowedSellerTableRates()) {
            return $this->resultRedirectFactory->create()->setPath(
                'marketplace/account/dashboard/',
                ['_secure' => $this->getRequest()->isSecure()]
            );
        }

        $loginUrl = $this->_url->getLoginUrl();
        if (!$this->_customerSession->authenticate($loginUrl)) {
            $this->_actionFlag->set('', self::FLAG_NO_DISPATCH, true);
        }
        return parent::dispatch($request);
    }

    /**
     * Get Seller Shiprate
     *
     * @return \Coditron\CustomShippingRate\Model\ShipTableRates
     * @throws LocalizedException
     */
    public function getSellerShiprate(): \Coditron\CustomShippingRate\Model\ShipTableRates
    {
        $sellerShiprate = $this->mpSellershiprateFactory->create();
        if (!empty($this->getRequest()->getParam("shiptablerates_id"))) {
            $sellerShiprate = $this->shiprateRepository->get($this->getRequest()->getParam("shiptablerates_id"));
        }

        return $sellerShiprate;
    }

    /**
     * Get Current Seller Id
     *
     * @return integer
     */
    public function getSellerId()
    {
        return (int) $this->_marketplaceHelper->getCustomerId();
    }
}
